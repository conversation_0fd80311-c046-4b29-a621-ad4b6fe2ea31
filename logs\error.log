2025-05-28 10:53:48 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:19 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:21 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:25 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 10:54:38 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:08:38 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:08:44 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:03 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 11:11:25 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:11:26 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:11:28 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:29 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 11:11:33 - Error: Get section amounts error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:51 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:21:04 - Error: Get users error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 5
2025-05-28 11:21:38 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:22:36 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
