2025-05-28 10:53:48 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:19 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:21 - Error: Activity log error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'medical_bill_system.user_activity_log' doesn't exist
2025-05-28 10:54:25 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 10:54:38 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:08:38 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:08:44 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:03 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 11:11:25 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:11:26 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:11:28 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:29 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 11:11:33 - Error: Get section amounts error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:11:51 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 11:21:04 - Error: Get users error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 5
2025-05-28 11:21:38 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:22:36 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:59:04 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:59:06 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:59:15 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 11:59:16 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 12:00:19 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 12:00:20 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 12:00:20 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:39:48 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:25 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:27 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:27 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:28 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:28 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:28 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:29 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:29 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:44:29 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:46:56 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:46:59 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:47:08 - Error: Get claim master error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:47:12 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:47:43 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:48:59 - Error: Get medical claims error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 14:49:06 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:10 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:11 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:11 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:12 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:12 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:58 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:58 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:49:59 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 14:50:32 - Error: Get doctors error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 4
2025-05-28 15:11:59 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 15:12:01 - Error: Get section amounts error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 6
2025-05-28 17:02:01 - Error: include(../includes/header.php): Failed to open stream: No such file or directory in D:\GitHub\MBPHP\debug_claim_upload.php on line 328
2025-05-28 17:02:01 - Error: include(): Failed opening '../includes/header.php' for inclusion (include_path='D:\dev\xampp\php\PEAR') in D:\GitHub\MBPHP\debug_claim_upload.php on line 328
2025-05-28 17:02:01 - Error: include(../includes/footer.php): Failed to open stream: No such file or directory in D:\GitHub\MBPHP\debug_claim_upload.php on line 358
2025-05-28 17:02:01 - Error: include(): Failed opening '../includes/footer.php' for inclusion (include_path='D:\dev\xampp\php\PEAR') in D:\GitHub\MBPHP\debug_claim_upload.php on line 358
2025-05-28 17:14:07 - Error: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\GitHub\MBPHP\config\config.php on line 98
2025-05-28 17:17:07 - Error: Get medical claims error: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'name' in where clause is ambiguous
2025-05-28 17:17:19 - Error: Get medical claims error: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'name' in where clause is ambiguous
2025-05-28 17:17:21 - Error: Get medical claims error: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'name' in where clause is ambiguous
2025-05-28 17:17:41 - Error: Get consultations error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 14
2025-05-28 17:22:33 - Error: Get users error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 5
2025-05-28 17:23:56 - Error: Get users error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 5
2025-05-28 17:24:04 - Error: Get users error: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''25' OFFSET '0'' at line 5
