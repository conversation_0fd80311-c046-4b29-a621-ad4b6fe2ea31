<?php
/**
 * Doctor List Management Page
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('dr_list')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Doctor List';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        $db = getDB();

        if ($action === 'add' || $action === 'edit') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $degree = sanitizeInput($_POST['degree'] ?? '');
            $mobile_no = sanitizeInput($_POST['mobile_no'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $address = sanitizeInput($_POST['address'] ?? '');

            // Validation
            if (empty($name)) {
                throw new Exception('Doctor name is required');
            }

            if ($action === 'add') {
                $stmt = $db->prepare("
                    INSERT INTO doctors_register (name, degree, mobile_no, email, address)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$name, $degree, $mobile_no, $email, $address]);
                $message = 'Doctor added successfully!';
                $messageType = 'success';
            } else {
                $id = (int)($_POST['id'] ?? 0);
                if ($id <= 0) {
                    throw new Exception('Invalid doctor ID');
                }

                $stmt = $db->prepare("
                    UPDATE doctors_register
                    SET name = ?, degree = ?, mobile_no = ?, email = ?, address = ?
                    WHERE id = ?
                ");
                $stmt->execute([$name, $degree, $mobile_no, $email, $address, $id]);
                $message = 'Doctor updated successfully!';
                $messageType = 'success';
            }
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('Invalid doctor ID');
            }

            $stmt = $db->prepare("UPDATE doctors_register SET status = 'inactive' WHERE id = ?");
            $stmt->execute([$id]);
            $message = 'Doctor deleted successfully!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Dr List error: " . $e->getMessage());
    }
}

// Get doctors list
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;

    $whereClause = "WHERE (status = 'active' OR status IS NULL)";
    $params = [];

    if (!empty($search)) {
        $whereClause .= " AND (name LIKE ? OR degree LIKE ? OR mobile_no LIKE ? OR email LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM doctors_register $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];

    // Get doctors
    $stmt = $db->prepare("
        SELECT * FROM doctors_register
        $whereClause
        ORDER BY name
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $doctors = $stmt->fetchAll();

    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    logError("Get doctors error: " . $e->getMessage());
    $doctors = [];
    $total = 0;
    $totalPages = 0;
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user-md me-2"></i>Doctor List Management
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Add/Edit Doctor Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Add New Doctor</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="doctorForm">
            <input type="hidden" name="action" value="add" id="formAction">
            <input type="hidden" name="id" value="" id="doctorId">

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Doctor Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="degree" class="form-label">Degree</label>
                        <input type="text" class="form-control" id="degree" name="degree" placeholder="e.g., MBBS, MD">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="mobile_no" class="form-label">Mobile No</label>
                        <input type="tel" class="form-control" id="mobile_no" name="mobile_no" pattern="[0-9]{10}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="address" class="form-label">Address</label>
                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Doctor
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Doctors List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Doctors List</h5>
        <div class="d-flex gap-2">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="Search doctors..."
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($doctors)): ?>
            <div class="text-center py-4">
                <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                <p class="text-muted">No doctors found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="doctorsTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Degree</th>
                            <th>Mobile No</th>
                            <th>Email</th>
                            <th>Address</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($doctors as $doctor): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($doctor['name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($doctor['degree']); ?></td>
                                <td><?php echo htmlspecialchars($doctor['mobile_no']); ?></td>
                                <td><?php echo htmlspecialchars($doctor['email']); ?></td>
                                <td><?php echo htmlspecialchars($doctor['address']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="editDoctor(<?php echo htmlspecialchars(json_encode($doctor)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteDoctor(<?php echo $doctor['id']; ?>, '<?php echo htmlspecialchars($doctor['name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Doctors pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteDoctorName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteDoctorId">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = "
<script>
function editDoctor(doctor) {
    document.getElementById('formAction').value = 'edit';
    document.getElementById('doctorId').value = doctor.id;
    document.getElementById('name').value = doctor.name;
    document.getElementById('degree').value = doctor.degree || '';
    document.getElementById('mobile_no').value = doctor.mobile_no || '';
    document.getElementById('email').value = doctor.email || '';
    document.getElementById('address').value = doctor.address || '';

    document.querySelector('.card-header h5').innerHTML = '<i class=\"fas fa-edit me-2\"></i>Edit Doctor';
    document.querySelector('button[type=\"submit\"]').innerHTML = '<i class=\"fas fa-save me-2\"></i>Update Doctor';

    document.getElementById('name').focus();
}

function clearForm() {
    document.getElementById('doctorForm').reset();
    document.getElementById('formAction').value = 'add';
    document.getElementById('doctorId').value = '';

    document.querySelector('.card-header h5').innerHTML = '<i class=\"fas fa-plus-circle me-2\"></i>Add New Doctor';
    document.querySelector('button[type=\"submit\"]').innerHTML = '<i class=\"fas fa-save me-2\"></i>Save Doctor';
}

function deleteDoctor(id, name) {
    document.getElementById('deleteDoctorId').value = id;
    document.getElementById('deleteDoctorName').textContent = name;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('doctorsTable')) {
        initDataTable('#doctorsTable', {
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [5] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
