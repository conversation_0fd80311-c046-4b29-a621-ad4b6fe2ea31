<?php
/**
 * Dashboard page for Medical Bill Management System
 */

require_once '../config/config.php';

// Check authentication
checkAuth();

$pageTitle = 'Dashboard';

// Get dashboard statistics
try {
    $db = getDB();
    
    // Get counts by status
    $stats = [];
    
    // Bills at different user levels
    $statusCounts = [
        'SDN' => 'Bills at SDN User',
        'DN' => 'Bills at DN User', 
        'CR' => 'Bills at CR User',
        'CO' => 'Bills at CO User',
        'DR' => 'Submitted to Doctor',
        'Return' => 'Returned Bills',
        'In Payment' => 'Process in Payment',
        'Paid' => 'Paid Bills'
    ];
    
    foreach ($statusCounts as $status => $label) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM medical_claim_register WHERE ph_status = ?");
        $stmt->execute([$status]);
        $result = $stmt->fetch();
        $stats[$status] = [
            'count' => $result['count'],
            'label' => $label
        ];
    }
    
    // Total claims
    $stmt = $db->query("SELECT COUNT(*) as count FROM medical_claim_register");
    $totalClaims = $stmt->fetch()['count'];
    
    // Total amount
    $stmt = $db->query("SELECT SUM(claim_amount) as total FROM medical_claim_register");
    $totalAmount = $stmt->fetch()['total'] ?? 0;
    
    // Recent activities (last 10 entries)
    $stmt = $db->query("
        SELECT claim_no, name, claim_amount, ph_status, created_at 
        FROM medical_claim_register 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();
    
    // Monthly statistics
    $stmt = $db->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as count,
            SUM(claim_amount) as amount
        FROM medical_claim_register 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
    ");
    $monthlyStats = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("Dashboard error: " . $e->getMessage());
    $stats = [];
    $totalClaims = 0;
    $totalAmount = 0;
    $recentActivities = [];
    $monthlyStats = [];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            <small class="text-muted">Welcome back, <?php echo $_SESSION['user_name']; ?></small>
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <!-- Total Claims -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo number_format($totalClaims); ?></div>
                    <div class="stats-label">Total Claims</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-file-medical fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Total Amount -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($totalAmount); ?></div>
                    <div class="stats-label">Total Amount</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-rupee-sign fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pending Bills -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo $stats['SDN']['count'] + $stats['DN']['count'] + $stats['CR']['count'] + $stats['CO']['count']; ?></div>
                    <div class="stats-label">Pending Bills</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Paid Bills -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #8e44ad, #9b59b6);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo $stats['Paid']['count']; ?></div>
                    <div class="stats-label">Paid Bills</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Breakdown -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Bills Status Breakdown</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($stats as $status => $data): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="border rounded p-3 text-center">
                                <h4 class="text-primary mb-1"><?php echo $data['count']; ?></h4>
                                <small class="text-muted"><?php echo $data['label']; ?></small>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar" style="width: <?php echo $totalClaims > 0 ? ($data['count'] / $totalClaims * 100) : 0; ?>%"></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span>Submitted to Doctor</span>
                    <span class="badge bg-info"><?php echo $stats['DR']['count']; ?></span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>In Payment Process</span>
                    <span class="badge bg-warning"><?php echo $stats['In Payment']['count']; ?></span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>Returned Bills</span>
                    <span class="badge bg-danger"><?php echo $stats['Return']['count']; ?></span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Completed</span>
                    <span class="badge bg-success"><?php echo $stats['Paid']['count']; ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activities</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentActivities)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activities found.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Claim No</th>
                                    <th>Employee</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentActivities as $activity): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($activity['claim_no']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($activity['name']); ?></td>
                                        <td><?php echo formatCurrency($activity['claim_amount']); ?></td>
                                        <td>
                                            <span class="status-badge bg-<?php 
                                                echo match($activity['ph_status']) {
                                                    'Paid' => 'success',
                                                    'Return' => 'danger',
                                                    'DR' => 'info',
                                                    'In Payment' => 'warning',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo $activity['ph_status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($activity['created_at']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Monthly Overview</h5>
            </div>
            <div class="card-body">
                <?php if (empty($monthlyStats)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No monthly data available.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($monthlyStats as $month): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong><?php echo date('M Y', strtotime($month['month'] . '-01')); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo $month['count']; ?> claims</small>
                            </div>
                            <div class="text-end">
                                <strong><?php echo formatCurrency($month['amount']); ?></strong>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
