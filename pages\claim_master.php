<?php
/**
 * Claim Master Management Page - Excel Upload
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('claim_master')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Claim Master';
$message = '';
$messageType = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    try {
        $file = $_FILES['excel_file'];

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error');
        }

        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ['xls', 'xlsx', 'csv'])) {
            throw new Exception('Only Excel (.xls, .xlsx) and CSV files are allowed');
        }

        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size exceeds maximum limit of ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }

        // Create upload directory if not exists
        if (!is_dir(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
        }

        // Move uploaded file
        $fileName = 'claim_master_' . date('Y-m-d_H-i-s') . '.' . $fileExtension;
        $filePath = UPLOAD_DIR . $fileName;

        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('Failed to save uploaded file');
        }

        // Process the file
        $db = getDB();
        $recordsProcessed = 0;
        $recordsUpdated = 0;

        // Function to convert date format
        function convertDate($dateStr) {
            if (empty($dateStr)) return null;

            // Handle different date formats
            $dateStr = trim($dateStr);

            // Try different date formats
            $formats = [
                'd-M-y',    // 17-May-24
                'd-M-Y',    // 17-May-2024
                'd/m/Y',    // 17/05/2024
                'd-m-Y',    // 17-05-2024
                'Y-m-d',    // 2024-05-17
                'm/d/Y',    // 05/17/2024
            ];

            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateStr);
                if ($date !== false) {
                    return $date->format('Y-m-d');
                }
            }

            // Try strtotime as fallback
            $timestamp = strtotime($dateStr);
            if ($timestamp !== false) {
                return date('Y-m-d', $timestamp);
            }

            return null;
        }

        if ($fileExtension === 'csv') {
            $handle = fopen($filePath, 'r');
            $header = fgetcsv($handle); // Get header row

            // Map header columns to indices for flexible column mapping
            $columnMap = [];
            foreach ($header as $index => $columnName) {
                $columnName = strtolower(trim($columnName));
                $columnName = str_replace([' ', '_'], '', $columnName); // Remove spaces and underscores
                $columnMap[$columnName] = $index;
            }

            // Define expected column mappings (flexible)
            $expectedColumns = [
                'claimno' => ['claimno', 'claim_no', 'claimnumber'],
                'employeenumber' => ['employeenumber', 'employee_number', 'empno', 'emp_no'],
                'name' => ['name', 'employeename', 'employee_name'],
                'designation' => ['designation', 'post', 'position'],
                'organization' => ['organization', 'dept', 'department', 'office'],
                'claimtype' => ['claimtype', 'claim_type', 'type'],
                'dateofapplication' => ['dateofapplication', 'date_of_application', 'applicationdate', 'application_date'],
                'startdateofexpense' => ['startdateofexpense', 'start_date_of_expense', 'startdate', 'start_date'],
                'enddateofexpense' => ['enddateofexpense', 'end_date_of_expense', 'enddate', 'end_date'],
                'claimamount' => ['claimamount', 'claim_amount', 'amount'],
                'sanctionedamount' => ['sanctionedamount', 'sanctioned_amount', 'sanctioned'],
                'disallowedamount' => ['disallowedamount', 'disallowed_amount', 'disallowed'],
                'status' => ['status', 'claimstatus', 'claim_status'],
                'patientname' => ['patientname', 'patient_name', 'patient'],
                'relation' => ['relation', 'relationship'],
                'treatmenttype' => ['treatmenttype', 'treatment_type', 'treatmemttype', 'treatnemt_type'] // Handle typo
            ];

            // Find column indices
            $columns = [];
            foreach ($expectedColumns as $key => $variations) {
                $columns[$key] = null;
                foreach ($variations as $variation) {
                    if (isset($columnMap[$variation])) {
                        $columns[$key] = $columnMap[$variation];
                        break;
                    }
                }
            }



            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= 10) { // Ensure minimum required columns
                    $claimNo = isset($columns['claimno']) ? trim($data[$columns['claimno']]) : '';

                    if (!empty($claimNo)) {
                        // Extract data using column mapping
                        $employeeNumber = isset($columns['employeenumber']) ? trim($data[$columns['employeenumber']]) : '';
                        $name = isset($columns['name']) ? trim($data[$columns['name']]) : '';
                        $designation = isset($columns['designation']) ? trim($data[$columns['designation']]) : '';
                        $organization = isset($columns['organization']) ? trim($data[$columns['organization']]) : '';
                        $claimType = isset($columns['claimtype']) ? trim($data[$columns['claimtype']]) : '';
                        $dateOfApplication = isset($columns['dateofapplication']) ? convertDate($data[$columns['dateofapplication']]) : null;
                        $startDate = isset($columns['startdateofexpense']) ? convertDate($data[$columns['startdateofexpense']]) : null;
                        $endDate = isset($columns['enddateofexpense']) ? convertDate($data[$columns['enddateofexpense']]) : null;
                        $claimAmount = isset($columns['claimamount']) && is_numeric($data[$columns['claimamount']]) ? $data[$columns['claimamount']] : 0;
                        $sanctionedAmount = isset($columns['sanctionedamount']) && is_numeric($data[$columns['sanctionedamount']]) ? $data[$columns['sanctionedamount']] : 0;
                        $disallowedAmount = isset($columns['disallowedamount']) && is_numeric($data[$columns['disallowedamount']]) ? $data[$columns['disallowedamount']] : 0;
                        $status = isset($columns['status']) ? trim($data[$columns['status']]) : '';
                        $patientName = isset($columns['patientname']) ? trim($data[$columns['patientname']]) : '';
                        $relation = isset($columns['relation']) ? trim($data[$columns['relation']]) : '';
                        $treatmentType = isset($columns['treatmenttype']) ? trim($data[$columns['treatmenttype']]) : '';

                        // Check if claim exists
                        $checkStmt = $db->prepare("SELECT id FROM claim_master WHERE claim_no = ?");
                        $checkStmt->execute([$claimNo]);

                        if ($checkStmt->fetch()) {
                            // Update existing record
                            $stmt = $db->prepare("
                                UPDATE claim_master SET
                                employee_number = ?, name = ?, designation = ?, organization = ?,
                                claim_type = ?, date_of_application = ?, start_date_of_expense = ?,
                                end_date_of_expense = ?, claim_amount = ?, sanctioned_amount = ?,
                                disallowed_amount = ?, status = ?, patient_name = ?, relation = ?,
                                treatment_type = ?, updated_at = NOW()
                                WHERE claim_no = ?
                            ");
                            $stmt->execute([
                                $employeeNumber, $name, $designation, $organization, $claimType,
                                $dateOfApplication, $startDate, $endDate, $claimAmount,
                                $sanctionedAmount, $disallowedAmount, $status, $patientName,
                                $relation, $treatmentType, $claimNo
                            ]);
                            $recordsUpdated++;
                        } else {
                            // Insert new record
                            $stmt = $db->prepare("
                                INSERT INTO claim_master (
                                    claim_no, employee_number, name, designation, organization,
                                    claim_type, date_of_application, start_date_of_expense,
                                    end_date_of_expense, claim_amount, sanctioned_amount,
                                    disallowed_amount, status, patient_name, relation, treatment_type
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $claimNo, $employeeNumber, $name, $designation, $organization,
                                $claimType, $dateOfApplication, $startDate, $endDate,
                                $claimAmount, $sanctionedAmount, $disallowedAmount, $status,
                                $patientName, $relation, $treatmentType
                            ]);
                        }

                        $recordsProcessed++;
                    }
                }
            }
            fclose($handle);
        } elseif (in_array($fileExtension, ['xls', 'xlsx'])) {
            // Process Excel files
            try {
                // Simple Excel processing without external libraries
                // Convert Excel to CSV first using a simple method

                if ($fileExtension === 'xlsx') {
                    // For XLSX files, we'll use a simple XML parsing approach
                    $zip = new ZipArchive();
                    if ($zip->open($filePath) === TRUE) {
                        // Get the shared strings
                        $sharedStrings = [];
                        if (($sharedStringsXML = $zip->getFromName('xl/sharedStrings.xml')) !== false) {
                            $xml = simplexml_load_string($sharedStringsXML);
                            foreach ($xml->si as $si) {
                                $sharedStrings[] = (string)$si->t;
                            }
                        }

                        // Get the worksheet data
                        if (($worksheetXML = $zip->getFromName('xl/worksheets/sheet1.xml')) !== false) {
                            $xml = simplexml_load_string($worksheetXML);
                            $rows = [];

                            foreach ($xml->sheetData->row as $row) {
                                $rowData = [];
                                foreach ($row->c as $cell) {
                                    $value = '';
                                    if (isset($cell->v)) {
                                        $cellValue = (string)$cell->v;
                                        // Check if it's a shared string
                                        if (isset($cell['t']) && $cell['t'] == 's') {
                                            $value = isset($sharedStrings[$cellValue]) ? $sharedStrings[$cellValue] : '';
                                        } else {
                                            $value = $cellValue;
                                        }
                                    }
                                    $rowData[] = $value;
                                }
                                $rows[] = $rowData;
                            }

                            if (!empty($rows)) {
                                $header = array_shift($rows); // First row as header

                                // Process the same way as CSV
                                $columnMap = [];
                                foreach ($header as $index => $columnName) {
                                    $columnName = strtolower(trim($columnName));
                                    $columnName = str_replace([' ', '_'], '', $columnName);
                                    $columnMap[$columnName] = $index;
                                }

                                // Define expected columns (same as CSV)
                                $expectedColumns = [
                                    'claimno' => ['claimno', 'claim_no', 'claimnumber'],
                                    'employeenumber' => ['employeenumber', 'employee_number', 'empno', 'emp_no'],
                                    'name' => ['name', 'employeename', 'employee_name'],
                                    'designation' => ['designation', 'post', 'position'],
                                    'organization' => ['organization', 'dept', 'department', 'office'],
                                    'claimtype' => ['claimtype', 'claim_type', 'type'],
                                    'dateofapplication' => ['dateofapplication', 'date_of_application', 'applicationdate', 'application_date'],
                                    'startdateofexpense' => ['startdateofexpense', 'start_date_of_expense', 'startdate', 'start_date'],
                                    'enddateofexpense' => ['enddateofexpense', 'end_date_of_expense', 'enddate', 'end_date'],
                                    'claimamount' => ['claimamount', 'claim_amount', 'amount'],
                                    'sanctionedamount' => ['sanctionedamount', 'sanctioned_amount', 'sanctioned'],
                                    'disallowedamount' => ['disallowedamount', 'disallowed_amount', 'disallowed'],
                                    'status' => ['status', 'claimstatus', 'claim_status'],
                                    'patientname' => ['patientname', 'patient_name', 'patient'],
                                    'relation' => ['relation', 'relationship'],
                                    'treatmenttype' => ['treatmenttype', 'treatment_type', 'treatmemttype', 'treatnemt_type']
                                ];

                                // Find column indices
                                $columns = [];
                                foreach ($expectedColumns as $key => $variations) {
                                    $columns[$key] = null;
                                    foreach ($variations as $variation) {
                                        if (isset($columnMap[$variation])) {
                                            $columns[$key] = $columnMap[$variation];
                                            break;
                                        }
                                    }
                                }

                                // Process each row
                                foreach ($rows as $data) {
                                    if (count($data) >= 10) {
                                        $claimNo = isset($columns['claimno']) ? trim($data[$columns['claimno']]) : '';

                                        if (!empty($claimNo)) {
                                            // Extract data using column mapping
                                            $employeeNumber = isset($columns['employeenumber']) ? trim($data[$columns['employeenumber']]) : '';
                                            $name = isset($columns['name']) ? trim($data[$columns['name']]) : '';
                                            $designation = isset($columns['designation']) ? trim($data[$columns['designation']]) : '';
                                            $organization = isset($columns['organization']) ? trim($data[$columns['organization']]) : '';
                                            $claimType = isset($columns['claimtype']) ? trim($data[$columns['claimtype']]) : '';
                                            $dateOfApplication = isset($columns['dateofapplication']) ? convertDate($data[$columns['dateofapplication']]) : null;
                                            $startDate = isset($columns['startdateofexpense']) ? convertDate($data[$columns['startdateofexpense']]) : null;
                                            $endDate = isset($columns['enddateofexpense']) ? convertDate($data[$columns['enddateofexpense']]) : null;
                                            $claimAmount = isset($columns['claimamount']) && is_numeric($data[$columns['claimamount']]) ? $data[$columns['claimamount']] : 0;
                                            $sanctionedAmount = isset($columns['sanctionedamount']) && is_numeric($data[$columns['sanctionedamount']]) ? $data[$columns['sanctionedamount']] : 0;
                                            $disallowedAmount = isset($columns['disallowedamount']) && is_numeric($data[$columns['disallowedamount']]) ? $data[$columns['disallowedamount']] : 0;
                                            $status = isset($columns['status']) ? trim($data[$columns['status']]) : '';
                                            $patientName = isset($columns['patientname']) ? trim($data[$columns['patientname']]) : '';
                                            $relation = isset($columns['relation']) ? trim($data[$columns['relation']]) : '';
                                            $treatmentType = isset($columns['treatmenttype']) ? trim($data[$columns['treatmenttype']]) : '';

                                            // Check if claim exists
                                            $checkStmt = $db->prepare("SELECT id FROM claim_master WHERE claim_no = ?");
                                            $checkStmt->execute([$claimNo]);

                                            if ($checkStmt->fetch()) {
                                                // Update existing record
                                                $stmt = $db->prepare("
                                                    UPDATE claim_master SET
                                                    employee_number = ?, name = ?, designation = ?, organization = ?,
                                                    claim_type = ?, date_of_application = ?, start_date_of_expense = ?,
                                                    end_date_of_expense = ?, claim_amount = ?, sanctioned_amount = ?,
                                                    disallowed_amount = ?, status = ?, patient_name = ?, relation = ?,
                                                    treatment_type = ?, updated_at = NOW()
                                                    WHERE claim_no = ?
                                                ");
                                                $stmt->execute([
                                                    $employeeNumber, $name, $designation, $organization, $claimType,
                                                    $dateOfApplication, $startDate, $endDate, $claimAmount,
                                                    $sanctionedAmount, $disallowedAmount, $status, $patientName,
                                                    $relation, $treatmentType, $claimNo
                                                ]);
                                                $recordsUpdated++;
                                            } else {
                                                // Insert new record
                                                $stmt = $db->prepare("
                                                    INSERT INTO claim_master (
                                                        claim_no, employee_number, name, designation, organization,
                                                        claim_type, date_of_application, start_date_of_expense,
                                                        end_date_of_expense, claim_amount, sanctioned_amount,
                                                        disallowed_amount, status, patient_name, relation, treatment_type
                                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                                ");
                                                $stmt->execute([
                                                    $claimNo, $employeeNumber, $name, $designation, $organization,
                                                    $claimType, $dateOfApplication, $startDate, $endDate,
                                                    $claimAmount, $sanctionedAmount, $disallowedAmount, $status,
                                                    $patientName, $relation, $treatmentType
                                                ]);
                                            }

                                            $recordsProcessed++;
                                        }
                                    }
                                }
                            }
                        }
                        $zip->close();
                    } else {
                        throw new Exception('Unable to open Excel file');
                    }
                } else {
                    // For XLS files, we'll try a simple approach or suggest converting to XLSX
                    throw new Exception('XLS files are not supported. Please convert to XLSX or CSV format.');
                }
            } catch (Exception $e) {
                throw new Exception('Error processing Excel file: ' . $e->getMessage());
            }
        }

        $message = "File uploaded successfully! Processed: $recordsProcessed records, Updated: $recordsUpdated records.";
        $messageType = 'success';

    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Claim Master upload error: " . $e->getMessage());
    }
}

// Get claim master records
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;

    $whereClause = "WHERE 1=1";
    $params = [];

    if (!empty($search)) {
        $whereClause .= " AND (claim_no LIKE ? OR employee_number LIKE ? OR name LIKE ? OR patient_name LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM claim_master $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];

    // Get records
    $stmt = $db->prepare("
        SELECT * FROM claim_master
        $whereClause
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);
    $claims = $stmt->fetchAll();

    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    logError("Get claim master error: " . $e->getMessage());
    $claims = [];
    $total = 0;
    $totalPages = 0;
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-file-medical me-2"></i>Claim Master Management
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- File Upload Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Claim Master File</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data" id="uploadForm">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label">Select Excel/CSV File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file"
                               accept=".xlsx,.csv" required>
                        <div class="form-text">
                            Supported formats: .xlsx, .csv (Max size: <?php echo MAX_FILE_SIZE / 1024 / 1024; ?>MB)
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-upload me-2"></i>Upload File
                    </button>
                </div>
            </div>
        </form>

        <div class="mt-3">
            <h6>Expected File Format:</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Claim No</th>
                            <th>Employee Number</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Organization</th>
                            <th>Claim Type</th>
                            <th>Date Of Application</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned Amount</th>
                            <th>Disallowed Amount</th>
                            <th>Status</th>
                            <th>Patient Name</th>
                            <th>Relation</th>
                            <th>Treatment Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="text-muted">
                            <td>CLM2024001</td>
                            <td>EMP001</td>
                            <td>John Doe</td>
                            <td>Manager</td>
                            <td>IT Dept</td>
                            <td>Medical</td>
                            <td>2024-01-15</td>
                            <td>2024-01-10</td>
                            <td>2024-01-14</td>
                            <td>5000.00</td>
                            <td>4500.00</td>
                            <td>500.00</td>
                            <td>Approved</td>
                            <td>John Doe</td>
                            <td>Self</td>
                            <td>OPD</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Claims List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Claim Master Records
            <span class="badge bg-primary"><?php echo number_format($total); ?></span>
        </h5>
        <div class="d-flex gap-2">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="Search claims..."
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($claims)): ?>
            <div class="text-center py-4">
                <i class="fas fa-file-medical fa-3x text-muted mb-3"></i>
                <p class="text-muted">No claim records found. Upload an Excel file to get started.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="claimsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Name</th>
                            <th>Patient</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($claims as $claim): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($claim['claim_no']); ?></strong></td>
                                <td><?php echo htmlspecialchars($claim['employee_number']); ?></td>
                                <td><?php echo htmlspecialchars($claim['name']); ?></td>
                                <td><?php echo htmlspecialchars($claim['patient_name']); ?></td>
                                <td><?php echo formatCurrency($claim['claim_amount']); ?></td>
                                <td><?php echo formatCurrency($claim['sanctioned_amount']); ?></td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo match(strtolower($claim['status'])) {
                                            'approved' => 'success',
                                            'pending' => 'warning',
                                            'rejected' => 'danger',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php echo htmlspecialchars($claim['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($claim['date_of_application']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Claims pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
// File upload validation
document.getElementById('excel_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size / 1024 / 1024; // Convert to MB
        const maxSize = " . (MAX_FILE_SIZE / 1024 / 1024) . ";

        if (fileSize > maxSize) {
            alert('File size exceeds maximum limit of ' + maxSize + 'MB');
            e.target.value = '';
            return;
        }

        const allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xls|xlsx|csv)$/i)) {
            alert('Please select a valid Excel or CSV file');
            e.target.value = '';
            return;
        }
    }
});

// Show loading on form submit
document.getElementById('uploadForm').addEventListener('submit', function() {
    showLoading();
});

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('claimsTable')) {
        initDataTable('#claimsTable', {
            order: [[7, 'desc']],
            pageLength: 50
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
