<?php
/**
 * Export claims data to CSV
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../pages/login.php');
    exit();
}

try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $statusFilter = $_GET['status'] ?? '';
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (claim_no LIKE ? OR employee_number LIKE ? OR name LIKE ? OR patient_name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    if (!empty($statusFilter)) {
        $whereClause .= " AND ph_status = ?";
        $params[] = $statusFilter;
    }
    
    // Role-based filtering
    $userRole = getUserRole();
    if ($userRole !== 'Admin' && $userRole !== 'CO') {
        $allowedStatuses = match($userRole) {
            'SDN' => ['SDN'],
            'DN' => ['SDN', 'DN'],
            'CR' => ['SDN', 'DN', 'CR'],
            default => ['SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return']
        };
        
        $statusPlaceholders = str_repeat('?,', count($allowedStatuses) - 1) . '?';
        $whereClause .= " AND ph_status IN ($statusPlaceholders)";
        $params = array_merge($params, $allowedStatuses);
    }
    
    $stmt = $db->prepare("
        SELECT 
            claim_no, employee_number, name, designation, organization,
            patient_name, relation, disease_name, dr_name, treatment_type,
            claim_amount, sanctioned_amount, disallowed_amount,
            hospital_stay, ph_status, date_of_application, claim_received_date,
            start_date_of_expense, end_date_of_expense, dr_voucher_no, dr_amount,
            paid_salary_month, created_at
        FROM medical_claim_register 
        $whereClause 
        ORDER BY created_at DESC
    ");
    $stmt->execute($params);
    $claims = $stmt->fetchAll();
    
    // Set headers for CSV download
    $filename = 'medical_claims_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add CSV headers
    $headers = [
        'Claim No', 'Employee Number', 'Name', 'Designation', 'Organization',
        'Patient Name', 'Relation', 'Disease Name', 'Doctor Name', 'Treatment Type',
        'Claim Amount', 'Sanctioned Amount', 'Disallowed Amount',
        'Hospital Stay', 'Status', 'Application Date', 'Received Date',
        'Start Date', 'End Date', 'Dr Voucher No', 'Dr Amount',
        'Paid Salary Month', 'Created Date'
    ];
    fputcsv($output, $headers);
    
    // Add data rows
    foreach ($claims as $claim) {
        $row = [
            $claim['claim_no'],
            $claim['employee_number'],
            $claim['name'],
            $claim['designation'],
            $claim['organization'],
            $claim['patient_name'],
            $claim['relation'],
            $claim['disease_name'],
            $claim['dr_name'],
            $claim['treatment_type'],
            $claim['claim_amount'],
            $claim['sanctioned_amount'],
            $claim['disallowed_amount'],
            ucfirst($claim['hospital_stay']),
            $claim['ph_status'],
            $claim['date_of_application'],
            $claim['claim_received_date'],
            $claim['start_date_of_expense'],
            $claim['end_date_of_expense'],
            $claim['dr_voucher_no'],
            $claim['dr_amount'],
            $claim['paid_salary_month'],
            $claim['created_at']
        ];
        fputcsv($output, $row);
    }
    
    fclose($output);
    
} catch (Exception $e) {
    logError("Export claims error: " . $e->getMessage());
    header('Location: ../pages/medical_claim_register.php?error=export_failed');
    exit();
}
?>
