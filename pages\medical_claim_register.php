<?php
/**
 * Medical Claim Register - View all processed claims
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('medical_claim_register')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Medical Claim Register';
$message = '';
$messageType = '';

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $action = $_POST['action'];
        $claimId = (int)($_POST['claim_id'] ?? 0);
        
        if ($action === 'update_status' && $claimId > 0) {
            $newStatus = $_POST['new_status'] ?? '';
            $validStatuses = ['SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return'];
            
            if (in_array($newStatus, $validStatuses)) {
                $stmt = $db->prepare("UPDATE medical_claim_register SET ph_status = ? WHERE id = ?");
                $stmt->execute([$newStatus, $claimId]);
                $message = 'Status updated successfully!';
                $messageType = 'success';
            } else {
                throw new Exception('Invalid status');
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Medical claim register error: " . $e->getMessage());
    }
}

// Get claims with filters
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $statusFilter = $_GET['status'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (claim_no LIKE ? OR employee_number LIKE ? OR name LIKE ? OR patient_name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    if (!empty($statusFilter)) {
        $whereClause .= " AND ph_status = ?";
        $params[] = $statusFilter;
    }
    
    // Role-based filtering
    $userRole = getUserRole();
    if ($userRole !== 'Admin' && $userRole !== 'CO') {
        // Users can only see claims at their level or below
        $allowedStatuses = match($userRole) {
            'SDN' => ['SDN'],
            'DN' => ['SDN', 'DN'],
            'CR' => ['SDN', 'DN', 'CR'],
            default => ['SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return']
        };
        
        $statusPlaceholders = str_repeat('?,', count($allowedStatuses) - 1) . '?';
        $whereClause .= " AND ph_status IN ($statusPlaceholders)";
        $params = array_merge($params, $allowedStatuses);
    }
    
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM medical_claim_register $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get claims
    $stmt = $db->prepare("
        SELECT mcr.*, u.name as processed_by_name
        FROM medical_claim_register mcr
        LEFT JOIN users u ON mcr.processed_by = u.id
        $whereClause 
        ORDER BY mcr.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $claims = $stmt->fetchAll();
    
    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    logError("Get medical claims error: " . $e->getMessage());
    $claims = [];
    $total = 0;
    $totalPages = 0;
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-clipboard-list me-2"></i>Medical Claim Register
            <span class="badge bg-primary"><?php echo number_format($total); ?></span>
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Claim No, Employee, Name..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status Filter</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <?php foreach (PH_STATUS as $key => $label): ?>
                        <option value="<?php echo $key; ?>" <?php echo $statusFilter === $key ? 'selected' : ''; ?>>
                            <?php echo $label; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="medical_claim_register.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Claims Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Medical Claims</h5>
    </div>
    <div class="card-body">
        <?php if (empty($claims)): ?>
            <div class="text-center py-4">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <p class="text-muted">No medical claims found.</p>
                <?php if (hasPermission('entry_form')): ?>
                    <a href="entry_form.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Entry
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="claimsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Patient</th>
                            <th>Disease</th>
                            <th>Doctor</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned</th>
                            <th>Status</th>
                            <th>Hospital Stay</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($claims as $claim): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($claim['claim_no']); ?></strong>
                                    <br><small class="text-muted"><?php echo formatDate($claim['claim_received_date']); ?></small>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($claim['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($claim['employee_number']); ?></small>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($claim['patient_name']); ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($claim['relation']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($claim['disease_name']); ?></td>
                                <td><?php echo htmlspecialchars($claim['dr_name']); ?></td>
                                <td><?php echo formatCurrency($claim['claim_amount']); ?></td>
                                <td><?php echo formatCurrency($claim['sanctioned_amount']); ?></td>
                                <td>
                                    <span class="status-badge bg-<?php 
                                        echo match($claim['ph_status']) {
                                            'Paid' => 'success',
                                            'Return' => 'danger',
                                            'DR' => 'info',
                                            'In Payment' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php echo $claim['ph_status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $claim['hospital_stay'] === 'yes' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($claim['hospital_stay']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewClaim(<?php echo $claim['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if (hasPermission('dr_forwarding') && in_array($claim['ph_status'], ['SDN', 'DN', 'CR', 'CO'])): ?>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="updateStatus(<?php echo $claim['id']; ?>, 'DR')" title="Forward to Doctor">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (getUserRole() === 'Admin' || getUserRole() === 'CO'): ?>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <?php foreach (PH_STATUS as $status => $label): ?>
                                                        <?php if ($status !== $claim['ph_status']): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="#" 
                                                                   onclick="updateStatus(<?php echo $claim['id']; ?>, '<?php echo $status; ?>')">
                                                                    <?php echo $label; ?>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Claims pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($statusFilter); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- View Claim Modal -->
<div class="modal fade" id="viewClaimModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Claim Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="claimDetails">
                <!-- Claim details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = "
<script>
function updateStatus(claimId, newStatus) {
    if (confirm('Are you sure you want to update the status?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type='hidden' name='action' value='update_status'>
            <input type='hidden' name='claim_id' value='\${claimId}'>
            <input type='hidden' name='new_status' value='\${newStatus}'>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewClaim(claimId) {
    showLoading();
    
    fetch('../ajax/get_claim_details.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'claim_id=' + claimId
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            document.getElementById('claimDetails').innerHTML = data.html;
            const modal = new bootstrap.Modal(document.getElementById('viewClaimModal'));
            modal.show();
        } else {
            alert(data.message || 'Error loading claim details');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('Error loading claim details');
    });
}

function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '../ajax/export_claims.php?' + params.toString();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('claimsTable')) {
        initDataTable('#claimsTable', {
            order: [[0, 'desc']],
            pageLength: 50,
            columnDefs: [
                { orderable: false, targets: [9] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
