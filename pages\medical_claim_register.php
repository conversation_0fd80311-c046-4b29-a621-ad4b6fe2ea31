<?php
/**
 * Medical Claim Register - View all processed claims
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('medical_claim_register')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Medical Claim Register';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $action = $_POST['action'];
        $claimId = (int)($_POST['claim_id'] ?? 0);

        if ($action === 'update_status' && $claimId > 0) {
            $newStatus = $_POST['new_status'] ?? '';
            $validStatuses = ['SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return'];

            if (in_array($newStatus, $validStatuses)) {
                $stmt = $db->prepare("UPDATE medical_claim_register SET ph_status = ? WHERE id = ?");
                $stmt->execute([$newStatus, $claimId]);
                $message = 'Status updated successfully!';
                $messageType = 'success';
            } else {
                throw new Exception('Invalid status');
            }
        } elseif ($action === 'edit_claim' && $claimId > 0) {
            // Handle claim editing
            $claimReceivedDate = $_POST['claim_received_date'] ?? '';
            $diseaseName = sanitizeInput($_POST['disease_name'] ?? '');
            $drName = sanitizeInput($_POST['dr_name'] ?? '');
            $sanctionedAmount = (float)($_POST['sanctioned_amount'] ?? 0);
            $hospitalStay = $_POST['hospital_stay'] ?? 'no';
            $paidSalaryMonth = sanitizeInput($_POST['paid_salary_month'] ?? '');
            $drVoucherNo = sanitizeInput($_POST['dr_voucher_no'] ?? '');
            $drAmount = (float)($_POST['dr_amount'] ?? 0);

            // Validation
            if (empty($claimReceivedDate) || empty($diseaseName) || empty($drName) || $sanctionedAmount <= 0) {
                throw new Exception('Please fill all required fields');
            }

            // Get current claim data to calculate disallowed amount
            $stmt = $db->prepare("SELECT claim_amount FROM medical_claim_register WHERE id = ?");
            $stmt->execute([$claimId]);
            $currentClaim = $stmt->fetch();

            if (!$currentClaim) {
                throw new Exception('Claim not found');
            }

            $disallowedAmount = $currentClaim['claim_amount'] - $sanctionedAmount;

            // Update claim
            $stmt = $db->prepare("
                UPDATE medical_claim_register SET
                claim_received_date = ?, disease_name = ?, dr_name = ?,
                sanctioned_amount = ?, disallowed_amount = ?, hospital_stay = ?,
                paid_salary_month = ?, dr_voucher_no = ?, dr_amount = ?,
                updated_at = NOW()
                WHERE id = ?
            ");

            $stmt->execute([
                $claimReceivedDate, $diseaseName, $drName, $sanctionedAmount,
                $disallowedAmount, $hospitalStay, $paidSalaryMonth,
                $drVoucherNo, $drAmount, $claimId
            ]);

            $message = 'Medical claim updated successfully!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Medical claim register error: " . $e->getMessage());
    }
}

// Get claims with filters
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $statusFilter = $_GET['status'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;

    $whereClause = "WHERE 1=1";
    $params = [];

    if (!empty($search)) {
        $whereClause .= " AND (claim_no LIKE ? OR employee_number LIKE ? OR name LIKE ? OR patient_name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }

    if (!empty($statusFilter)) {
        $whereClause .= " AND ph_status = ?";
        $params[] = $statusFilter;
    }

    // Role-based filtering
    $userRole = getUserRole();
    if ($userRole !== 'Admin' && $userRole !== 'CO') {
        // Users can only see claims at their level or below
        $allowedStatuses = match($userRole) {
            'SDN' => ['SDN'],
            'DN' => ['SDN', 'DN'],
            'CR' => ['SDN', 'DN', 'CR'],
            default => ['SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return']
        };

        $statusPlaceholders = str_repeat('?,', count($allowedStatuses) - 1) . '?';
        $whereClause .= " AND ph_status IN ($statusPlaceholders)";
        $params = array_merge($params, $allowedStatuses);
    }

    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM medical_claim_register $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];

    // Get claims
    $stmt = $db->prepare("
        SELECT mcr.*, u.name as processed_by_name
        FROM medical_claim_register mcr
        LEFT JOIN users u ON mcr.processed_by = u.id
        $whereClause
        ORDER BY mcr.created_at DESC
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);
    $claims = $stmt->fetchAll();

    $totalPages = ceil($total / $limit);

    // Get doctors for edit form
    $stmt = $db->query("SELECT name FROM doctors_register WHERE (status = 'active' OR status IS NULL) ORDER BY name");
    $doctors = $stmt->fetchAll();

} catch (Exception $e) {
    logError("Get medical claims error: " . $e->getMessage());
    $claims = [];
    $total = 0;
    $totalPages = 0;
    $doctors = [];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-clipboard-list me-2"></i>Medical Claim Register
            <span class="badge bg-primary"><?php echo number_format($total); ?></span>
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search"
                       placeholder="Claim No, Employee, Name..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status Filter</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <?php foreach (PH_STATUS as $key => $label): ?>
                        <option value="<?php echo $key; ?>" <?php echo $statusFilter === $key ? 'selected' : ''; ?>>
                            <?php echo $label; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="medical_claim_register.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Claims Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Medical Claims</h5>
    </div>
    <div class="card-body">
        <?php if (empty($claims)): ?>
            <div class="text-center py-4">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <p class="text-muted">No medical claims found.</p>
                <?php if (hasPermission('entry_form')): ?>
                    <a href="entry_form.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Entry
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="claimsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Patient</th>
                            <th>Disease</th>
                            <th>Doctor</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned</th>
                            <th>Status</th>
                            <th>Hospital Stay</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($claims as $claim): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($claim['claim_no']); ?></strong>
                                    <br><small class="text-muted"><?php echo formatDate($claim['claim_received_date']); ?></small>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($claim['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($claim['employee_number']); ?></small>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($claim['patient_name']); ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($claim['relation']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($claim['disease_name']); ?></td>
                                <td><?php echo htmlspecialchars($claim['dr_name']); ?></td>
                                <td><?php echo formatCurrency($claim['claim_amount']); ?></td>
                                <td><?php echo formatCurrency($claim['sanctioned_amount']); ?></td>
                                <td>
                                    <span class="status-badge bg-<?php
                                        echo match($claim['ph_status']) {
                                            'Paid' => 'success',
                                            'Return' => 'danger',
                                            'DR' => 'info',
                                            'In Payment' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php echo $claim['ph_status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $claim['hospital_stay'] === 'yes' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($claim['hospital_stay']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewClaim(<?php echo $claim['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if (hasPermission('entry_form')): ?>
                                            <button type="button" class="btn btn-sm btn-outline-warning"
                                                    onclick="editClaim(<?php echo htmlspecialchars(json_encode($claim)); ?>)" title="Edit Claim">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (hasPermission('dr_forwarding') && in_array($claim['ph_status'], ['SDN', 'DN', 'CR', 'CO'])): ?>
                                            <button type="button" class="btn btn-sm btn-outline-success"
                                                    onclick="updateStatus(<?php echo $claim['id']; ?>, 'DR')" title="Forward to Doctor">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (getUserRole() === 'Admin' || getUserRole() === 'CO'): ?>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <?php foreach (PH_STATUS as $status => $label): ?>
                                                        <?php if ($status !== $claim['ph_status']): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="#"
                                                                   onclick="updateStatus(<?php echo $claim['id']; ?>, '<?php echo $status; ?>')">
                                                                    <?php echo $label; ?>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Claims pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($statusFilter); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- View Claim Modal -->
<div class="modal fade" id="viewClaimModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Claim Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="claimDetails">
                <!-- Claim details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Edit Claim Modal -->
<div class="modal fade" id="editClaimModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Medical Claim</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editClaimForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_claim">
                    <input type="hidden" name="claim_id" id="editClaimId">

                    <!-- Basic Information (Read-only) -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Claim No</label>
                            <input type="text" class="form-control" id="editClaimNo" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Employee</label>
                            <input type="text" class="form-control" id="editEmployeeInfo" readonly>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Patient Name</label>
                            <input type="text" class="form-control" id="editPatientName" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Relation</label>
                            <input type="text" class="form-control" id="editRelation" readonly>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Claim Amount</label>
                            <input type="number" class="form-control" id="editClaimAmount" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="editClaimReceivedDate" class="form-label">Claim Received Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="editClaimReceivedDate" name="claim_received_date" required>
                        </div>
                    </div>

                    <!-- Editable Fields -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editDiseaseName" class="form-label">Disease Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editDiseaseName" name="disease_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="editDrName" class="form-label">Dr. Name <span class="text-danger">*</span></label>
                            <select class="form-select" id="editDrName" name="dr_name" required>
                                <option value="">Select Doctor</option>
                                <?php foreach ($doctors as $doctor): ?>
                                    <option value="<?php echo htmlspecialchars($doctor['name']); ?>">
                                        <?php echo htmlspecialchars($doctor['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="editSanctionedAmount" class="form-label">Sanctioned Amount <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="editSanctionedAmount" name="sanctioned_amount"
                                   step="0.01" required onchange="calculateEditDisallowed()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Disallowed Amount</label>
                            <input type="number" class="form-control" id="editDisallowedAmount" readonly>
                            <div class="form-text">Auto-calculated</div>
                        </div>
                        <div class="col-md-4">
                            <label for="editHospitalStay" class="form-label">Hospital Stay</label>
                            <select class="form-select" id="editHospitalStay" name="hospital_stay">
                                <option value="no">No</option>
                                <option value="yes">Yes</option>
                            </select>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="editPaidSalaryMonth" class="form-label">Paid Salary Month</label>
                            <input type="text" class="form-control" id="editPaidSalaryMonth" name="paid_salary_month"
                                   placeholder="e.g., Jan-2024">
                        </div>
                        <div class="col-md-4">
                            <label for="editDrVoucherNo" class="form-label">Dr. Voucher No</label>
                            <input type="text" class="form-control" id="editDrVoucherNo" name="dr_voucher_no">
                        </div>
                        <div class="col-md-4">
                            <label for="editDrAmount" class="form-label">Dr. Amount</label>
                            <input type="number" class="form-control" id="editDrAmount" name="dr_amount" step="0.01">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Claim
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalJS = "
<script>
function updateStatus(claimId, newStatus) {
    if (confirm('Are you sure you want to update the status?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type='hidden' name='action' value='update_status'>
            <input type='hidden' name='claim_id' value='\${claimId}'>
            <input type='hidden' name='new_status' value='\${newStatus}'>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewClaim(claimId) {
    showLoading();

    fetch('../ajax/get_claim_details.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'claim_id=' + claimId
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            document.getElementById('claimDetails').innerHTML = data.html;
            const modal = new bootstrap.Modal(document.getElementById('viewClaimModal'));
            modal.show();
        } else {
            alert(data.message || 'Error loading claim details');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('Error loading claim details');
    });
}

function editClaim(claim) {
    // Populate the edit form with claim data
    document.getElementById('editClaimId').value = claim.id;
    document.getElementById('editClaimNo').value = claim.claim_no;
    document.getElementById('editEmployeeInfo').value = claim.employee_number + ' - ' + claim.name;
    document.getElementById('editPatientName').value = claim.patient_name || '';
    document.getElementById('editRelation').value = claim.relation || '';
    document.getElementById('editClaimAmount').value = claim.claim_amount || '';
    document.getElementById('editClaimReceivedDate').value = claim.claim_received_date || '';
    document.getElementById('editDiseaseName').value = claim.disease_name || '';
    document.getElementById('editDrName').value = claim.dr_name || '';
    document.getElementById('editSanctionedAmount').value = claim.sanctioned_amount || '';
    document.getElementById('editHospitalStay').value = claim.hospital_stay || 'no';
    document.getElementById('editPaidSalaryMonth').value = claim.paid_salary_month || '';
    document.getElementById('editDrVoucherNo').value = claim.dr_voucher_no || '';
    document.getElementById('editDrAmount').value = claim.dr_amount || '';

    // Calculate disallowed amount
    calculateEditDisallowed();

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editClaimModal'));
    modal.show();
}

function calculateEditDisallowed() {
    const claimAmount = parseFloat(document.getElementById('editClaimAmount').value) || 0;
    const sanctionedAmount = parseFloat(document.getElementById('editSanctionedAmount').value) || 0;
    const disallowedAmount = claimAmount - sanctionedAmount;

    document.getElementById('editDisallowedAmount').value = disallowedAmount.toFixed(2);
}

function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '../ajax/export_claims.php?' + params.toString();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('claimsTable')) {
        initDataTable('#claimsTable', {
            order: [[0, 'desc']],
            pageLength: 50,
            columnDefs: [
                { orderable: false, targets: [9] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
