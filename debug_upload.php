<?php
/**
 * Debug CSV upload process
 */

require_once 'config/config.php';

// Check authentication
checkAuth();

echo "<h1>Debug CSV Upload Process</h1>";

// Check if claim_master table exists
try {
    $db = getDB();
    
    echo "<h2>Step 1: Check claim_master table</h2>";
    $stmt = $db->query("SHOW TABLES LIKE 'claim_master'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Table 'claim_master' does not exist!</p>";
        echo "<p>Creating table...</p>";
        
        $createSQL = "
        CREATE TABLE claim_master (
            id INT AUTO_INCREMENT PRIMARY KEY,
            claim_no VARCHAR(50) UNIQUE NOT NULL,
            employee_number VARCHAR(50) NOT NULL,
            name VARCHAR(100) NOT NULL,
            designation VARCHAR(100),
            organization VARCHAR(100),
            claim_type VARCHAR(50),
            date_of_application DATE,
            start_date_of_expense DATE,
            end_date_of_expense DATE,
            claim_amount DECIMAL(10,2),
            sanctioned_amount DECIMAL(10,2),
            disallowed_amount DECIMAL(10,2),
            status VARCHAR(50),
            patient_name VARCHAR(100),
            relation VARCHAR(50),
            treatment_type VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_claim_no (claim_no),
            INDEX idx_employee_number (employee_number)
        )";
        
        $db->exec($createSQL);
        echo "<p style='color: green;'>✅ Table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ Table exists</p>";
    }
    
    // Check table structure
    echo "<h2>Step 2: Table Structure</h2>";
    $stmt = $db->query("DESCRIBE claim_master");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Count existing records
    $stmt = $db->query("SELECT COUNT(*) as total FROM claim_master");
    $total = $stmt->fetch()['total'];
    echo "<p><strong>Current records in table: $total</strong></p>";
    
    if ($total > 0) {
        echo "<h3>Sample Records:</h3>";
        $stmt = $db->query("SELECT * FROM claim_master ORDER BY created_at DESC LIMIT 5");
        $records = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Claim No</th><th>Employee</th><th>Name</th><th>Amount</th><th>Status</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['claim_no']) . "</td>";
            echo "<td>" . htmlspecialchars($record['employee_number']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . number_format($record['claim_amount'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($record['status']) . "</td>";
            echo "<td>" . $record['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Database Error:</strong> " . $e->getMessage() . "</p>";
}

// Test CSV processing with sample data
echo "<h2>Step 3: Test CSV Processing</h2>";

// Create a test CSV content based on your data
$testCSV = "Claim No,Employee Number,Name,Designation,Organization,Claim Type,Date Of Application,Start Date Of Expense,End Date Of Expense,Claim Amount,Sanctioned Amount,Disallowed Amount,Status,PatientName,Relation,Treatnemt Type
3916733,5869,Mr. SANJAYBHAI LALJIBHAI PATEL,Executive Engineer 11,**********:Bhavnagar Division:Technical,Medical Claim,17-May-24,10-May-24,10-May-24,3326,0,3326,With Mr. Mehulkumar Sharadchandra Joshi,Mr. SANJAYBHAI LALJIBHAI PATEL,Self,Allopathic";

// Save test CSV to temporary file
$tempFile = 'temp_test.csv';
file_put_contents($tempFile, $testCSV);

echo "<p><strong>Test CSV Content:</strong></p>";
echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($testCSV);
echo "</pre>";

try {
    // Process the test CSV
    $handle = fopen($tempFile, 'r');
    $header = fgetcsv($handle);
    
    echo "<p><strong>Headers found:</strong></p>";
    echo "<ol>";
    foreach ($header as $index => $columnName) {
        echo "<li>[$index] " . htmlspecialchars($columnName) . "</li>";
    }
    echo "</ol>";
    
    // Map header columns
    $columnMap = [];
    foreach ($header as $index => $columnName) {
        $columnName = strtolower(trim($columnName));
        $columnName = str_replace([' ', '_'], '', $columnName);
        $columnMap[$columnName] = $index;
    }
    
    echo "<p><strong>Column mapping:</strong></p>";
    echo "<pre>" . print_r($columnMap, true) . "</pre>";
    
    // Define expected columns
    $expectedColumns = [
        'claimno' => ['claimno', 'claim_no', 'claimnumber'],
        'employeenumber' => ['employeenumber', 'employee_number', 'empno', 'emp_no'],
        'name' => ['name', 'employeename', 'employee_name'],
        'designation' => ['designation', 'post', 'position'],
        'organization' => ['organization', 'dept', 'department', 'office'],
        'claimtype' => ['claimtype', 'claim_type', 'type'],
        'dateofapplication' => ['dateofapplication', 'date_of_application', 'applicationdate', 'application_date'],
        'startdateofexpense' => ['startdateofexpense', 'start_date_of_expense', 'startdate', 'start_date'],
        'enddateofexpense' => ['enddateofexpense', 'end_date_of_expense', 'enddate', 'end_date'],
        'claimamount' => ['claimamount', 'claim_amount', 'amount'],
        'sanctionedamount' => ['sanctionedamount', 'sanctioned_amount', 'sanctioned'],
        'disallowedamount' => ['disallowedamount', 'disallowed_amount', 'disallowed'],
        'status' => ['status', 'claimstatus', 'claim_status'],
        'patientname' => ['patientname', 'patient_name', 'patient'],
        'relation' => ['relation', 'relationship'],
        'treatmenttype' => ['treatmenttype', 'treatment_type', 'treatmemttype', 'treatnemt_type']
    ];
    
    // Find column indices
    $columns = [];
    foreach ($expectedColumns as $key => $variations) {
        $columns[$key] = null;
        foreach ($variations as $variation) {
            if (isset($columnMap[$variation])) {
                $columns[$key] = $columnMap[$variation];
                break;
            }
        }
    }
    
    echo "<p><strong>Column indices found:</strong></p>";
    echo "<pre>" . print_r($columns, true) . "</pre>";
    
    // Process data row
    $data = fgetcsv($handle);
    echo "<p><strong>Data row:</strong></p>";
    echo "<ol>";
    foreach ($data as $index => $value) {
        echo "<li>[$index] " . htmlspecialchars($value) . "</li>";
    }
    echo "</ol>";
    
    // Extract values
    $claimNo = isset($columns['claimno']) ? trim($data[$columns['claimno']]) : '';
    $employeeNumber = isset($columns['employeenumber']) ? trim($data[$columns['employeenumber']]) : '';
    $name = isset($columns['name']) ? trim($data[$columns['name']]) : '';
    
    echo "<p><strong>Extracted values:</strong></p>";
    echo "<ul>";
    echo "<li>Claim No: '$claimNo'</li>";
    echo "<li>Employee Number: '$employeeNumber'</li>";
    echo "<li>Name: '$name'</li>";
    echo "</ul>";
    
    // Test date conversion
    function convertDate($dateStr) {
        if (empty($dateStr)) return null;
        
        $dateStr = trim($dateStr);
        $formats = [
            'd-M-y',    // 17-May-24
            'd-M-Y',    // 17-May-2024
            'd/m/Y',    // 17/05/2024
            'd-m-Y',    // 17-05-2024
            'Y-m-d',    // 2024-05-17
            'm/d/Y',    // 05/17/2024
        ];
        
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateStr);
            if ($date !== false) {
                return $date->format('Y-m-d');
            }
        }
        
        $timestamp = strtotime($dateStr);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }
        
        return null;
    }
    
    $dateOfApplication = isset($columns['dateofapplication']) ? convertDate($data[$columns['dateofapplication']]) : null;
    echo "<p><strong>Date conversion test:</strong></p>";
    echo "<ul>";
    echo "<li>Original: '" . (isset($columns['dateofapplication']) ? $data[$columns['dateofapplication']] : 'N/A') . "'</li>";
    echo "<li>Converted: '$dateOfApplication'</li>";
    echo "</ul>";
    
    fclose($handle);
    unlink($tempFile); // Clean up
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Processing Error:</strong> " . $e->getMessage() . "</p>";
}

// Check upload directory
echo "<h2>Step 4: Check Upload Directory</h2>";
$uploadDir = 'assets/uploads/';
if (!is_dir($uploadDir)) {
    echo "<p style='color: red;'>❌ Upload directory does not exist: $uploadDir</p>";
    echo "<p>Creating directory...</p>";
    if (mkdir($uploadDir, 0755, true)) {
        echo "<p style='color: green;'>✅ Directory created</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create directory</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Upload directory exists: $uploadDir</p>";
    echo "<p>Directory permissions: " . substr(sprintf('%o', fileperms($uploadDir)), -4) . "</p>";
}

// Check constants
echo "<h2>Step 5: Check Constants</h2>";
echo "<ul>";
echo "<li>MAX_FILE_SIZE: " . (defined('MAX_FILE_SIZE') ? number_format(MAX_FILE_SIZE / 1024 / 1024, 2) . ' MB' : 'NOT DEFINED') . "</li>";
echo "<li>UPLOAD_DIR: " . (defined('UPLOAD_DIR') ? UPLOAD_DIR : 'NOT DEFINED') . "</li>";
echo "</ul>";

?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        margin: 20px; 
        background: #f8f9fa; 
    }
    h1, h2, h3 { 
        color: #333; 
        border-bottom: 2px solid #007bff; 
        padding-bottom: 5px; 
    }
    table { 
        background: white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        margin: 10px 0;
    }
    th, td { 
        padding: 8px; 
        text-align: left; 
    }
    th { 
        background: #f0f0f0; 
    }
    .btn {
        display: inline-block;
        padding: 10px 20px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin: 10px 5px;
    }
    .btn:hover {
        background: #0056b3;
    }
</style>

<div style="margin: 20px 0;">
    <a href="pages/claim_master.php" class="btn">🔄 Try Claim Master Again</a>
    <a href="setup.php" class="btn">⚙️ Run Setup</a>
</div>
