<?php
/**
 * Configuration file for Medical Bill Management System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/database.php';

// Application settings
define('APP_NAME', 'Medical Bill Management System');
define('APP_VERSION', '1.0.0');
define('DEVELOPER', 'Areli');

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../assets/uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_EXTENSIONS', ['xls', 'xlsx', 'csv']);

// Pagination settings
define('RECORDS_PER_PAGE', 25);

// Date format
define('DATE_FORMAT', 'Y-m-d');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');

// User roles
define('USER_ROLES', [
    'SDN' => 'Subdivision User',
    'DN' => 'Division User', 
    'CR' => 'Circle User',
    'CO' => 'Corporate User',
    'Admin' => 'Administrator'
]);

// Status options
define('PH_STATUS', [
    'SDN' => 'At SDN User',
    'DN' => 'At DN User',
    'CR' => 'At CR User', 
    'CO' => 'At CO User',
    'DR' => 'Submitted to Doctor',
    'In Payment' => 'Process in Payment',
    'Paid' => 'Paid',
    'Return' => 'Returned'
]);

// Helper functions
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function getUserRole() {
    return $_SESSION['user_role'] ?? null;
}

function hasPermission($permission) {
    if (!isLoggedIn()) return false;
    
    $role = getUserRole();
    if ($role === 'Admin') return true;
    
    // Get role permissions from database
    $db = getDB();
    $stmt = $db->prepare("SELECT permissions FROM roles WHERE role_name = ?");
    $stmt->execute([$role]);
    $result = $stmt->fetch();
    
    if ($result) {
        $permissions = json_decode($result['permissions'], true);
        return isset($permissions[$permission]) && $permissions[$permission];
    }
    
    return false;
}

function redirectToLogin() {
    header('Location: /pages/login.php');
    exit();
}

function checkAuth() {
    if (!isLoggedIn()) {
        redirectToLogin();
    }
}

function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

function formatCurrency($amount) {
    return '₹ ' . number_format($amount, 2);
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

function generateClaimNo() {
    return 'CLM' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

function generateVoucherNo() {
    return 'VCH' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// Error handling
function logError($message, $file = '', $line = '') {
    $log = date('Y-m-d H:i:s') . " - Error: $message";
    if ($file) $log .= " in $file";
    if ($line) $log .= " on line $line";
    $log .= PHP_EOL;
    
    error_log($log, 3, __DIR__ . '/../logs/error.log');
}

// Set error handler
set_error_handler(function($severity, $message, $file, $line) {
    logError($message, $file, $line);
});

// Create necessary directories
$dirs = [
    __DIR__ . '/../assets/uploads',
    __DIR__ . '/../logs'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>
