<?php
/**
 * User Management - Admin only page for managing system users
 */

require_once '../config/config.php';
require_once '../classes/User.php';

// Check authentication and permissions (Admin only)
checkAuth();
if (getUserRole() !== 'Admin') {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'User Management';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user = new User();
    
    try {
        if ($action === 'add' || $action === 'edit') {
            $userData = [
                'employee_no' => sanitizeInput($_POST['employee_no'] ?? ''),
                'name' => sanitizeInput($_POST['name'] ?? ''),
                'email' => sanitizeInput($_POST['email'] ?? ''),
                'mobile' => sanitizeInput($_POST['mobile'] ?? ''),
                'designation' => sanitizeInput($_POST['designation'] ?? ''),
                'organization' => sanitizeInput($_POST['organization'] ?? ''),
                'role' => $_POST['role'] ?? ''
            ];
            
            // Validation
            if (empty($userData['employee_no']) || empty($userData['name']) || empty($userData['role'])) {
                throw new Exception('Employee No, Name, and Role are required');
            }
            
            if (!in_array($userData['role'], ['SDN', 'DN', 'CR', 'CO', 'Admin'])) {
                throw new Exception('Invalid role selected');
            }
            
            if ($action === 'add') {
                $password = $_POST['password'] ?? '';
                if (empty($password)) {
                    throw new Exception('Password is required for new users');
                }
                if (strlen($password) < 6) {
                    throw new Exception('Password must be at least 6 characters long');
                }
                $userData['password'] = $password;
                
                if ($user->createUser($userData)) {
                    $message = 'User created successfully!';
                    $messageType = 'success';
                } else {
                    throw new Exception('Failed to create user. Employee No may already exist.');
                }
            } else {
                $userId = (int)($_POST['user_id'] ?? 0);
                if ($userId <= 0) {
                    throw new Exception('Invalid user ID');
                }
                
                // Don't allow changing password unless provided
                if (!empty($_POST['password'])) {
                    if (strlen($_POST['password']) < 6) {
                        throw new Exception('Password must be at least 6 characters long');
                    }
                    $userData['password'] = $_POST['password'];
                }
                
                if ($user->updateUser($userId, $userData)) {
                    $message = 'User updated successfully!';
                    $messageType = 'success';
                } else {
                    throw new Exception('Failed to update user');
                }
            }
        } elseif ($action === 'delete') {
            $userId = (int)($_POST['user_id'] ?? 0);
            if ($userId <= 0) {
                throw new Exception('Invalid user ID');
            }
            
            // Don't allow deleting the current admin user
            if ($userId == $_SESSION['user_id']) {
                throw new Exception('You cannot delete your own account');
            }
            
            if ($user->deleteUser($userId)) {
                $message = 'User deactivated successfully!';
                $messageType = 'success';
            } else {
                throw new Exception('Failed to delete user');
            }
        } elseif ($action === 'reset_password') {
            $userId = (int)($_POST['user_id'] ?? 0);
            $newPassword = $_POST['new_password'] ?? '';
            
            if ($userId <= 0) {
                throw new Exception('Invalid user ID');
            }
            
            if (empty($newPassword) || strlen($newPassword) < 6) {
                throw new Exception('Password must be at least 6 characters long');
            }
            
            $userData = ['password' => $newPassword];
            if ($user->updateUser($userId, $userData)) {
                $message = 'Password reset successfully!';
                $messageType = 'success';
            } else {
                throw new Exception('Failed to reset password');
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("User management error: " . $e->getMessage());
    }
}

// Get users list
$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$userObj = new User();
$usersData = $userObj->getUsers($page, RECORDS_PER_PAGE, $search);

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-users-cog me-2"></i>User Management
            <span class="badge bg-primary"><?php echo $usersData['total'] ?? 0; ?></span>
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Add/Edit User Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Add New User</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="userForm">
            <input type="hidden" name="action" value="add" id="formAction">
            <input type="hidden" name="user_id" value="" id="userId">
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="employee_no" class="form-label">Employee No <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="employee_no" name="employee_no" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <?php foreach (USER_ROLES as $roleKey => $roleLabel): ?>
                                <option value="<?php echo $roleKey; ?>"><?php echo $roleLabel; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="mobile" class="form-label">Mobile No</label>
                        <input type="tel" class="form-control" id="mobile" name="mobile" pattern="[0-9]{10}">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="designation" class="form-label">Designation</label>
                        <input type="text" class="form-control" id="designation" name="designation">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="organization" class="form-label">Organization</label>
                        <input type="text" class="form-control" id="organization" name="organization">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger" id="passwordRequired">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" minlength="6">
                        <div class="form-text">Minimum 6 characters. Leave blank when editing to keep current password.</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save User
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-users me-2"></i>System Users</h5>
        <div class="d-flex gap-2">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="Search users..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($usersData['users'])): ?>
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">No users found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>Employee No</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Email</th>
                            <th>Mobile</th>
                            <th>Designation</th>
                            <th>Organization</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($usersData['users'] as $userRecord): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($userRecord['employee_no']); ?></strong></td>
                                <td><?php echo htmlspecialchars($userRecord['name']); ?></td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($userRecord['role']) {
                                            'Admin' => 'danger',
                                            'CO' => 'primary',
                                            'CR' => 'info',
                                            'DN' => 'success',
                                            'SDN' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php echo $userRecord['role']; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($userRecord['email']); ?></td>
                                <td><?php echo htmlspecialchars($userRecord['mobile']); ?></td>
                                <td><?php echo htmlspecialchars($userRecord['designation']); ?></td>
                                <td><?php echo htmlspecialchars($userRecord['organization']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $userRecord['status'] === 'active' ? 'success' : 'danger'; ?>">
                                        <?php echo ucfirst($userRecord['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($userRecord['created_at']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editUser(<?php echo htmlspecialchars(json_encode($userRecord)); ?>)" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="resetPassword(<?php echo $userRecord['id']; ?>, '<?php echo htmlspecialchars($userRecord['name']); ?>')" title="Reset Password">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <?php if ($userRecord['id'] != $_SESSION['user_id']): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteUser(<?php echo $userRecord['id']; ?>, '<?php echo htmlspecialchars($userRecord['name']); ?>')" title="Deactivate User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($usersData['pages'] > 1): ?>
                <nav aria-label="Users pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $usersData['pages']; $i++): ?>
                            <li class="page-item <?php echo $i === $usersData['current_page'] ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deactivation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate <strong id="deleteUserName"></strong>?</p>
                <p class="text-muted">The user will no longer be able to access the system.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <button type="submit" class="btn btn-danger">Deactivate</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="user_id" id="resetUserId">
                    <p>Reset password for <strong id="resetUserName"></strong></p>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               required minlength="6" placeholder="Enter new password">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalJS = "
<script>
function editUser(user) {
    document.getElementById('formAction').value = 'edit';
    document.getElementById('userId').value = user.id;
    document.getElementById('employee_no').value = user.employee_no;
    document.getElementById('name').value = user.name;
    document.getElementById('email').value = user.email || '';
    document.getElementById('mobile').value = user.mobile || '';
    document.getElementById('designation').value = user.designation || '';
    document.getElementById('organization').value = user.organization || '';
    document.getElementById('role').value = user.role;
    document.getElementById('password').value = '';
    document.getElementById('password').required = false;
    document.getElementById('passwordRequired').style.display = 'none';
    
    document.querySelector('.card-header h5').innerHTML = '<i class=\"fas fa-user-edit me-2\"></i>Edit User';
    document.querySelector('button[type=\"submit\"]').innerHTML = '<i class=\"fas fa-save me-2\"></i>Update User';
    
    document.getElementById('employee_no').focus();
}

function clearForm() {
    document.getElementById('userForm').reset();
    document.getElementById('formAction').value = 'add';
    document.getElementById('userId').value = '';
    document.getElementById('password').required = true;
    document.getElementById('passwordRequired').style.display = 'inline';
    
    document.querySelector('.card-header h5').innerHTML = '<i class=\"fas fa-user-plus me-2\"></i>Add New User';
    document.querySelector('button[type=\"submit\"]').innerHTML = '<i class=\"fas fa-save me-2\"></i>Save User';
}

function deleteUser(userId, userName) {
    document.getElementById('deleteUserId').value = userId;
    document.getElementById('deleteUserName').textContent = userName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function resetPassword(userId, userName) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUserName').textContent = userName;
    document.getElementById('new_password').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('usersTable')) {
        initDataTable('#usersTable', {
            order: [[8, 'desc']],
            columnDefs: [
                { orderable: false, targets: [9] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
