<?php
/**
 * Fix LIMIT/OFFSET issues in all PHP files
 */

echo "<h1>Fixing LIMIT/OFFSET Issues in All Files</h1>";

$filesToFix = [
    'pages/consultation_register.php',
    'pages/section_amount.php',
    'pages/user_management.php',
    'pages/bill_wise_section.php',
    'pages/employee_wise_section.php'
];

$fixedFiles = [];
$errors = [];

foreach ($filesToFix as $file) {
    if (file_exists($file)) {
        echo "<h2>Checking: $file</h2>";
        
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Pattern 1: LIMIT ? OFFSET ? with params being added
        $pattern1 = '/LIMIT \? OFFSET \?\s*"\);\s*\$params\[\] = \$limit;\s*\$params\[\] = \$offset;\s*\$stmt->execute\(\$params\);/';
        $replacement1 = 'LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);';
        
        $content = preg_replace($pattern1, $replacement1, $content);
        
        // Pattern 2: More specific pattern for different variations
        $pattern2 = '/(\$stmt = \$db->prepare\("[\s\S]*?)LIMIT \? OFFSET \?\s*"\);\s*\$params\[\] = \$limit;\s*\$params\[\] = \$offset;/';
        $replacement2 = '$1LIMIT $limit OFFSET $offset
    ");';
        
        $content = preg_replace($pattern2, $replacement2, $content);
        
        if ($content !== $originalContent) {
            if (file_put_contents($file, $content)) {
                $fixedFiles[] = $file;
                echo "<p style='color: green;'>✅ Fixed $file</p>";
            } else {
                $errors[] = "Failed to write to $file";
                echo "<p style='color: red;'>❌ Failed to write to $file</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ No changes needed in $file</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ File not found: $file</p>";
    }
}

echo "<h2>Summary</h2>";
echo "<p><strong>Files fixed:</strong> " . count($fixedFiles) . "</p>";
if (!empty($fixedFiles)) {
    echo "<ul>";
    foreach ($fixedFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<p style='color: red;'><strong>Errors:</strong></p>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

echo "<h2>Test Results</h2>";
echo "<p>Now test your pages:</p>";
echo "<ul>";
echo "<li><a href='pages/dr_list.php' target='_blank'>Dr. List</a></li>";
echo "<li><a href='pages/medical_claim_register.php' target='_blank'>Medical Claim Register</a></li>";
echo "<li><a href='pages/claim_master.php' target='_blank'>Claim Master</a></li>";
echo "<li><a href='pages/consultation_register.php' target='_blank'>Consultation Register</a></li>";
echo "<li><a href='pages/section_amount.php' target='_blank'>Section Amount</a></li>";
echo "<li><a href='pages/user_management.php' target='_blank'>User Management</a></li>";
echo "</ul>";
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        margin: 20px; 
        background: #f8f9fa; 
    }
    h1, h2 { 
        color: #333; 
        border-bottom: 2px solid #007bff; 
        padding-bottom: 5px; 
    }
    a {
        color: #007bff;
        text-decoration: none;
        padding: 5px 10px;
        background: #e9ecef;
        border-radius: 3px;
        margin: 2px;
        display: inline-block;
    }
    a:hover {
        background: #007bff;
        color: white;
    }
</style>
