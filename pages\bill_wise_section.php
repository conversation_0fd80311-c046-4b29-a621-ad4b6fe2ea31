<?php
/**
 * Bill wise Section Amount Report
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('reports')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Bill wise Section Amount Report';

// Get filter parameters
$fromDate = $_GET['from_date'] ?? date('Y-m-01'); // First day of current month
$toDate = $_GET['to_date'] ?? date('Y-m-d'); // Today
$status = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Get report data
try {
    $db = getDB();
    
    $whereClause = "WHERE mcr.claim_received_date BETWEEN ? AND ?";
    $params = [$fromDate, $toDate];
    
    if (!empty($status)) {
        $whereClause .= " AND mcr.ph_status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereClause .= " AND (mcr.claim_no LIKE ? OR mcr.employee_number LIKE ? OR mcr.name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    // Get detailed bill data
    $stmt = $db->prepare("
        SELECT 
            mcr.claim_no,
            mcr.employee_number,
            mcr.name,
            mcr.designation,
            mcr.organization,
            mcr.patient_name,
            mcr.disease_name,
            mcr.dr_name,
            mcr.claim_amount,
            mcr.sanctioned_amount,
            mcr.disallowed_amount,
            mcr.hospital_stay,
            mcr.ph_status,
            mcr.claim_received_date,
            mcr.dr_voucher_no,
            mcr.dr_amount,
            sa.sanctioned_amount as section_sanctioned_amount,
            sa.disallowed_amount as section_disallowed_amount
        FROM medical_claim_register mcr
        LEFT JOIN section_amount sa ON mcr.claim_no = sa.claim_no
        $whereClause
        ORDER BY mcr.claim_received_date DESC, mcr.claim_no
    ");
    $stmt->execute($params);
    $bills = $stmt->fetchAll();
    
    // Calculate summary statistics
    $summary = [
        'total_bills' => count($bills),
        'total_claim_amount' => array_sum(array_column($bills, 'claim_amount')),
        'total_sanctioned_amount' => array_sum(array_column($bills, 'sanctioned_amount')),
        'total_disallowed_amount' => array_sum(array_column($bills, 'disallowed_amount')),
        'total_dr_amount' => array_sum(array_filter(array_column($bills, 'dr_amount'))),
        'hospital_stay_bills' => count(array_filter($bills, fn($bill) => $bill['hospital_stay'] === 'yes')),
        'consultation_bills' => count(array_filter($bills, fn($bill) => $bill['hospital_stay'] === 'no'))
    ];
    
    // Group by status
    $statusGroups = [];
    foreach ($bills as $bill) {
        $status = $bill['ph_status'];
        if (!isset($statusGroups[$status])) {
            $statusGroups[$status] = [
                'count' => 0,
                'total_amount' => 0,
                'sanctioned_amount' => 0
            ];
        }
        $statusGroups[$status]['count']++;
        $statusGroups[$status]['total_amount'] += $bill['claim_amount'];
        $statusGroups[$status]['sanctioned_amount'] += $bill['sanctioned_amount'];
    }
    
} catch (Exception $e) {
    logError("Bill wise report error: " . $e->getMessage());
    $bills = [];
    $summary = [
        'total_bills' => 0,
        'total_claim_amount' => 0,
        'total_sanctioned_amount' => 0,
        'total_disallowed_amount' => 0,
        'total_dr_amount' => 0,
        'hospital_stay_bills' => 0,
        'consultation_bills' => 0
    ];
    $statusGroups = [];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-bar me-2"></i>Bill wise Section Amount Report
        </h1>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Report Filters</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="from_date" class="form-label">From Date</label>
                <input type="date" class="form-control" id="from_date" name="from_date" value="<?php echo $fromDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="to_date" class="form-label">To Date</label>
                <input type="date" class="form-control" id="to_date" name="to_date" value="<?php echo $toDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <?php foreach (PH_STATUS as $key => $label): ?>
                        <option value="<?php echo $key; ?>" <?php echo $status === $key ? 'selected' : ''; ?>>
                            <?php echo $label; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Claim No, Employee..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Generate Report
                </button>
                <a href="bill_wise_section.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
                <button type="button" class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download me-1"></i>Export Excel
                </button>
                <button type="button" class="btn btn-info" onclick="printReport()">
                    <i class="fas fa-print me-1"></i>Print Report
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number"><?php echo number_format($summary['total_bills']); ?></div>
            <div class="stats-label">Total Bills</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #3498db, #2980b9);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_claim_amount']); ?></div>
            <div class="stats-label">Total Claim</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_sanctioned_amount']); ?></div>
            <div class="stats-label">Total Sanctioned</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_disallowed_amount']); ?></div>
            <div class="stats-label">Total Disallowed</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_dr_amount']); ?></div>
            <div class="stats-label">Total Dr. Payment</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
            <div class="stats-number"><?php echo $summary['hospital_stay_bills']; ?> / <?php echo $summary['consultation_bills']; ?></div>
            <div class="stats-label">Hospital / Consultation</div>
        </div>
    </div>
</div>

<!-- Status Breakdown -->
<?php if (!empty($statusGroups)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status Breakdown</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($statusGroups as $statusKey => $statusData): ?>
                <div class="col-md-3 mb-3">
                    <div class="border rounded p-3 text-center">
                        <h4 class="text-primary"><?php echo $statusData['count']; ?></h4>
                        <h6><?php echo PH_STATUS[$statusKey] ?? $statusKey; ?></h6>
                        <small class="text-muted"><?php echo formatCurrency($statusData['sanctioned_amount']); ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Bills Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Bill wise Details 
            <span class="badge bg-primary"><?php echo number_format($summary['total_bills']); ?></span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($bills)): ?>
            <div class="text-center py-4">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <p class="text-muted">No bills found for the selected criteria.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="billsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Patient</th>
                            <th>Disease</th>
                            <th>Doctor</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned</th>
                            <th>Disallowed</th>
                            <th>Dr. Amount</th>
                            <th>Hospital Stay</th>
                            <th>Status</th>
                            <th>Received Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bills as $bill): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($bill['claim_no']); ?></strong></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($bill['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($bill['employee_number']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($bill['patient_name']); ?></td>
                                <td><?php echo htmlspecialchars($bill['disease_name']); ?></td>
                                <td><?php echo htmlspecialchars($bill['dr_name']); ?></td>
                                <td><?php echo formatCurrency($bill['claim_amount']); ?></td>
                                <td><?php echo formatCurrency($bill['sanctioned_amount']); ?></td>
                                <td><?php echo formatCurrency($bill['disallowed_amount']); ?></td>
                                <td><?php echo $bill['dr_amount'] ? formatCurrency($bill['dr_amount']) : '-'; ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $bill['hospital_stay'] === 'yes' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($bill['hospital_stay']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge bg-<?php 
                                        echo match($bill['ph_status']) {
                                            'Paid' => 'success',
                                            'Return' => 'danger',
                                            'DR' => 'info',
                                            'In Payment' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php echo $bill['ph_status']; ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($bill['claim_received_date']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <th colspan="5">Total</th>
                            <th><?php echo formatCurrency($summary['total_claim_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_sanctioned_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_disallowed_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_dr_amount']); ?></th>
                            <th colspan="3"><?php echo number_format($summary['total_bills']); ?> Bills</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '../ajax/export_bill_wise_report.php?' + params.toString();
}

function printReport() {
    window.print();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('billsTable')) {
        initDataTable('#billsTable', {
            order: [[11, 'desc']],
            pageLength: 50,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            footerCallback: function (row, data, start, end, display) {
                // Keep the footer totals visible
            }
        });
    }
});

// Print styles
const printStyles = `
    <style>
        @media print {
            .sidebar, .navbar, .btn, .card-header .btn-group { display: none !important; }
            .main-content { margin-left: 0 !important; }
            .card { border: 1px solid #000 !important; }
            .table { font-size: 12px !important; }
            .stats-card { border: 1px solid #000 !important; margin-bottom: 10px !important; }
        }
    </style>
`;
document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
";

include '../includes/footer.php';
?>
