<?php
/**
 * AJAX handler for getting detailed claim information
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$claimId = (int)($_POST['claim_id'] ?? 0);

if ($claimId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid claim ID']);
    exit();
}

try {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT mcr.*, u.name as processed_by_name
        FROM medical_claim_register mcr
        LEFT JOIN users u ON mcr.processed_by = u.id
        WHERE mcr.id = ?
    ");
    $stmt->execute([$claimId]);
    $claim = $stmt->fetch();
    
    if (!$claim) {
        echo json_encode(['success' => false, 'message' => 'Claim not found']);
        exit();
    }
    
    // Generate HTML for claim details
    $html = "
    <div class='row'>
        <div class='col-md-6'>
            <h6 class='text-primary'>Basic Information</h6>
            <table class='table table-sm'>
                <tr><td><strong>Claim No:</strong></td><td>{$claim['claim_no']}</td></tr>
                <tr><td><strong>Employee No:</strong></td><td>{$claim['employee_number']}</td></tr>
                <tr><td><strong>Name:</strong></td><td>{$claim['name']}</td></tr>
                <tr><td><strong>Designation:</strong></td><td>{$claim['designation']}</td></tr>
                <tr><td><strong>Organization:</strong></td><td>{$claim['organization']}</td></tr>
                <tr><td><strong>Patient Name:</strong></td><td>{$claim['patient_name']}</td></tr>
                <tr><td><strong>Relation:</strong></td><td>{$claim['relation']}</td></tr>
            </table>
        </div>
        <div class='col-md-6'>
            <h6 class='text-primary'>Medical Information</h6>
            <table class='table table-sm'>
                <tr><td><strong>Disease Name:</strong></td><td>{$claim['disease_name']}</td></tr>
                <tr><td><strong>Doctor Name:</strong></td><td>{$claim['dr_name']}</td></tr>
                <tr><td><strong>Treatment Type:</strong></td><td>{$claim['treatment_type']}</td></tr>
                <tr><td><strong>Hospital Stay:</strong></td><td>" . ucfirst($claim['hospital_stay']) . "</td></tr>
                <tr><td><strong>Application Date:</strong></td><td>" . formatDate($claim['date_of_application']) . "</td></tr>
                <tr><td><strong>Received Date:</strong></td><td>" . formatDate($claim['claim_received_date']) . "</td></tr>
                <tr><td><strong>Expense Period:</strong></td><td>" . formatDate($claim['start_date_of_expense']) . " to " . formatDate($claim['end_date_of_expense']) . "</td></tr>
            </table>
        </div>
    </div>
    
    <div class='row mt-3'>
        <div class='col-md-6'>
            <h6 class='text-primary'>Financial Details</h6>
            <table class='table table-sm'>
                <tr><td><strong>Claim Amount:</strong></td><td>" . formatCurrency($claim['claim_amount']) . "</td></tr>
                <tr><td><strong>Sanctioned Amount:</strong></td><td>" . formatCurrency($claim['sanctioned_amount']) . "</td></tr>
                <tr><td><strong>Disallowed Amount:</strong></td><td>" . formatCurrency($claim['disallowed_amount']) . "</td></tr>
                <tr><td><strong>Dr. Amount:</strong></td><td>" . formatCurrency($claim['dr_amount']) . "</td></tr>
                <tr><td><strong>Dr. Voucher No:</strong></td><td>{$claim['dr_voucher_no']}</td></tr>
            </table>
        </div>
        <div class='col-md-6'>
            <h6 class='text-primary'>Status Information</h6>
            <table class='table table-sm'>
                <tr><td><strong>Current Status:</strong></td><td>
                    <span class='badge bg-" . match($claim['ph_status']) {
                        'Paid' => 'success',
                        'Return' => 'danger',
                        'DR' => 'info',
                        'In Payment' => 'warning',
                        default => 'secondary'
                    } . "'>{$claim['ph_status']}</span>
                </td></tr>
                <tr><td><strong>Processed By:</strong></td><td>{$claim['processed_by_name']}</td></tr>
                <tr><td><strong>Paid Salary Month:</strong></td><td>{$claim['paid_salary_month']}</td></tr>
                <tr><td><strong>Created Date:</strong></td><td>" . formatDate($claim['created_at']) . "</td></tr>
                <tr><td><strong>Last Updated:</strong></td><td>" . formatDate($claim['updated_at']) . "</td></tr>
            </table>
        </div>
    </div>";
    
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    logError("Get claim details error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching claim details'
    ]);
}
?>
