<?php
/**
 * Simple check for doctors_register table
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "<h2>Checking doctors_register table</h2>";
    
    // Check if table exists
    $stmt = $db->query("SHOW TABLES LIKE 'doctors_register'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Table 'doctors_register' does not exist!</p>";
        
        // Create the table
        echo "<p>Creating doctors_register table...</p>";
        $createTable = "
        CREATE TABLE doctors_register (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            degree VARCHAR(100),
            mobile_no VARCHAR(15),
            email VARCHAR(100),
            address TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $db->exec($createTable);
        echo "<p style='color: green;'>✅ Table created successfully!</p>";
        
        // Insert sample doctors
        $sampleDoctors = [
            ['Dr. Rajesh Kumar', 'MBBS, MD', '**********', '<EMAIL>', '123 Medical Street, City'],
            ['Dr. Priya Sharma', 'MBBS, MS', '**********', '<EMAIL>', '456 Health Avenue, City'],
            ['Dr. Amit Patel', 'MBBS, DM', '**********', '<EMAIL>', '789 Care Road, City']
        ];
        
        $stmt = $db->prepare("INSERT INTO doctors_register (name, degree, mobile_no, email, address) VALUES (?, ?, ?, ?, ?)");
        foreach ($sampleDoctors as $doctor) {
            $stmt->execute($doctor);
        }
        echo "<p style='color: green;'>✅ Sample doctors added!</p>";
    } else {
        echo "<p style='color: green;'>✅ Table 'doctors_register' exists</p>";
    }
    
    // Count total records
    $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register");
    $total = $stmt->fetch()['total'];
    echo "<p><strong>Total doctors in table: $total</strong></p>";
    
    // Count active records
    $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register WHERE status = 'active'");
    $active = $stmt->fetch()['total'];
    echo "<p><strong>Active doctors: $active</strong></p>";
    
    // Count records with NULL status
    $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register WHERE status IS NULL");
    $nullStatus = $stmt->fetch()['total'];
    echo "<p><strong>Doctors with NULL status: $nullStatus</strong></p>";
    
    if ($nullStatus > 0) {
        echo "<p style='color: orange;'>⚠️ Found doctors with NULL status. Fixing...</p>";
        $stmt = $db->prepare("UPDATE doctors_register SET status = 'active' WHERE status IS NULL");
        $stmt->execute();
        echo "<p style='color: green;'>✅ Fixed NULL status records</p>";
    }
    
    // Show all doctors
    $stmt = $db->query("SELECT id, name, degree, mobile_no, email, status, created_at FROM doctors_register ORDER BY id");
    $doctors = $stmt->fetchAll();
    
    if (!empty($doctors)) {
        echo "<h3>All Doctors in Database:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Name</th>";
        echo "<th style='padding: 8px;'>Degree</th>";
        echo "<th style='padding: 8px;'>Mobile</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($doctors as $doctor) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $doctor['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($doctor['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($doctor['degree']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($doctor['mobile_no']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($doctor['email']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($doctor['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . $doctor['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test the exact query from dr_list.php
    echo "<h3>Testing Dr. List Query:</h3>";
    $whereClause = "WHERE status = 'active'";
    $stmt = $db->prepare("SELECT * FROM doctors_register $whereClause ORDER BY name");
    $stmt->execute();
    $testDoctors = $stmt->fetchAll();
    
    echo "<p><strong>Query result count: " . count($testDoctors) . "</strong></p>";
    
    if (empty($testDoctors)) {
        echo "<p style='color: red;'>❌ No doctors found with the current query</p>";
        
        // Try alternative query
        echo "<p>Trying alternative query (including NULL status)...</p>";
        $whereClause = "WHERE (status = 'active' OR status IS NULL)";
        $stmt = $db->prepare("SELECT * FROM doctors_register $whereClause ORDER BY name");
        $stmt->execute();
        $altDoctors = $stmt->fetchAll();
        echo "<p><strong>Alternative query result count: " . count($altDoctors) . "</strong></p>";
    } else {
        echo "<p style='color: green;'>✅ Query working correctly</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Error Details:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        margin: 20px; 
        background: #f5f5f5; 
    }
    h2, h3 { 
        color: #333; 
        border-bottom: 2px solid #007bff; 
        padding-bottom: 5px; 
    }
    table { 
        background: white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
    }
    .btn {
        display: inline-block;
        padding: 10px 20px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin: 10px 5px;
    }
    .btn:hover {
        background: #0056b3;
    }
</style>

<div style="margin: 20px 0;">
    <a href="pages/dr_list.php" class="btn">← Back to Dr. List</a>
    <a href="setup.php" class="btn">Run Setup Again</a>
</div>
