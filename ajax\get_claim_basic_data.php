<?php
/**
 * AJAX handler for getting basic claim data (employee number and name)
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$claimNo = sanitizeInput($_POST['claim_no'] ?? '');

if (empty($claimNo)) {
    echo json_encode(['success' => false, 'message' => 'Claim number is required']);
    exit();
}

try {
    $db = getDB();
    $stmt = $db->prepare("SELECT employee_number, name FROM medical_claim_register WHERE claim_no = ?");
    $stmt->execute([$claimNo]);
    $claim = $stmt->fetch();
    
    if ($claim) {
        echo json_encode([
            'success' => true,
            'employee_number' => $claim['employee_number'],
            'name' => $claim['name']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Claim not found in Medical Claim Register'
        ]);
    }
} catch (Exception $e) {
    logError("Get basic claim data error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching claim data'
    ]);
}
?>
