<?php
/**
 * Comprehensive fix for doctors_register table issues
 */

require_once 'config/config.php';

echo "<h1>Doctors Table Diagnostic & Fix</h1>";

try {
    $db = getDB();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Step 1: Check if table exists
    echo "<h2>Step 1: Checking Table Existence</h2>";
    $stmt = $db->query("SHOW TABLES LIKE 'doctors_register'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Table 'doctors_register' does not exist!</p>";
        echo "<p>Creating table...</p>";
        
        $createSQL = "
        CREATE TABLE doctors_register (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            degree VARCHAR(100),
            mobile_no VARCHAR(15),
            email VARCHAR(100),
            address TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $db->exec($createSQL);
        echo "<p style='color: green;'>✅ Table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ Table exists</p>";
    }
    
    // Step 2: Check table structure
    echo "<h2>Step 2: Table Structure</h2>";
    $stmt = $db->query("DESCRIBE doctors_register");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 3: Count records
    echo "<h2>Step 3: Record Count</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register");
    $total = $stmt->fetch()['total'];
    echo "<p><strong>Total records: $total</strong></p>";
    
    if ($total == 0) {
        echo "<p style='color: orange;'>⚠️ No doctors found. Adding sample doctors...</p>";
        
        $sampleDoctors = [
            ['Dr. Rajesh Kumar', 'MBBS, MD', '**********', '<EMAIL>', '123 Medical Street, City'],
            ['Dr. Priya Sharma', 'MBBS, MS', '**********', '<EMAIL>', '456 Health Avenue, City'],
            ['Dr. Amit Patel', 'MBBS, DM', '**********', '<EMAIL>', '789 Care Road, City'],
            ['Dr. Sunita Verma', 'MBBS, DNB', '**********', '<EMAIL>', '321 Health Plaza, City'],
            ['Dr. Vikram Singh', 'MBBS, MCh', '**********', '<EMAIL>', '654 Medical Center, City']
        ];
        
        $stmt = $db->prepare("INSERT INTO doctors_register (name, degree, mobile_no, email, address, status) VALUES (?, ?, ?, ?, ?, 'active')");
        
        foreach ($sampleDoctors as $doctor) {
            $stmt->execute($doctor);
        }
        
        echo "<p style='color: green;'>✅ Added " . count($sampleDoctors) . " sample doctors</p>";
        
        // Recount
        $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register");
        $total = $stmt->fetch()['total'];
        echo "<p><strong>New total records: $total</strong></p>";
    }
    
    // Step 4: Check status values
    echo "<h2>Step 4: Status Analysis</h2>";
    $stmt = $db->query("SELECT status, COUNT(*) as count FROM doctors_register GROUP BY status");
    $statusCounts = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Status</th><th>Count</th></tr>";
    foreach ($statusCounts as $status) {
        echo "<tr>";
        echo "<td>" . ($status['status'] ?? 'NULL') . "</td>";
        echo "<td>" . $status['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Fix NULL status
    $stmt = $db->query("SELECT COUNT(*) as count FROM doctors_register WHERE status IS NULL OR status = ''");
    $nullCount = $stmt->fetch()['count'];
    
    if ($nullCount > 0) {
        echo "<p style='color: orange;'>⚠️ Found $nullCount doctors with NULL/empty status. Fixing...</p>";
        $stmt = $db->prepare("UPDATE doctors_register SET status = 'active' WHERE status IS NULL OR status = ''");
        $stmt->execute();
        echo "<p style='color: green;'>✅ Fixed status for $nullCount doctors</p>";
    }
    
    // Step 5: Show all doctors
    echo "<h2>Step 5: All Doctors</h2>";
    $stmt = $db->query("SELECT * FROM doctors_register ORDER BY id");
    $allDoctors = $stmt->fetchAll();
    
    if (empty($allDoctors)) {
        echo "<p style='color: red;'>❌ Still no doctors found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Degree</th><th>Mobile</th><th>Email</th><th>Status</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($allDoctors as $doctor) {
            echo "<tr>";
            echo "<td>" . $doctor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($doctor['name']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['degree']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['mobile_no']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['email']) . "</td>";
            echo "<td style='color: " . ($doctor['status'] === 'active' ? 'green' : 'red') . ";'>" . $doctor['status'] . "</td>";
            echo "<td>" . $doctor['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 6: Test the exact query from dr_list.php
    echo "<h2>Step 6: Testing Dr. List Query</h2>";
    
    // Test original query
    echo "<h3>Original Query (status = 'active'):</h3>";
    $stmt = $db->prepare("SELECT * FROM doctors_register WHERE status = 'active' ORDER BY name");
    $stmt->execute();
    $activeOnly = $stmt->fetchAll();
    echo "<p>Results: " . count($activeOnly) . " doctors</p>";
    
    // Test modified query
    echo "<h3>Modified Query (status = 'active' OR status IS NULL):</h3>";
    $stmt = $db->prepare("SELECT * FROM doctors_register WHERE (status = 'active' OR status IS NULL) ORDER BY name");
    $stmt->execute();
    $activeOrNull = $stmt->fetchAll();
    echo "<p>Results: " . count($activeOrNull) . " doctors</p>";
    
    // Test no filter query
    echo "<h3>No Filter Query:</h3>";
    $stmt = $db->prepare("SELECT * FROM doctors_register ORDER BY name");
    $stmt->execute();
    $noFilter = $stmt->fetchAll();
    echo "<p>Results: " . count($noFilter) . " doctors</p>";
    
    // Step 7: Final verification
    echo "<h2>Step 7: Final Verification</h2>";
    if (count($activeOnly) > 0) {
        echo "<p style='color: green;'>✅ Dr. List should work with active doctors</p>";
    } elseif (count($activeOrNull) > 0) {
        echo "<p style='color: orange;'>⚠️ Dr. List needs the modified query (already applied)</p>";
    } elseif (count($noFilter) > 0) {
        echo "<p style='color: red;'>❌ There's an issue with the status field</p>";
        
        // Force fix all statuses
        echo "<p>Force fixing all doctor statuses...</p>";
        $stmt = $db->prepare("UPDATE doctors_register SET status = 'active'");
        $stmt->execute();
        echo "<p style='color: green;'>✅ All doctors set to active status</p>";
    } else {
        echo "<p style='color: red;'>❌ No doctors found at all - this shouldn't happen after adding samples</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        margin: 20px; 
        background: #f8f9fa; 
    }
    h1, h2, h3 { 
        color: #333; 
        border-bottom: 2px solid #007bff; 
        padding-bottom: 5px; 
    }
    table { 
        background: white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
    }
    th, td { 
        padding: 8px; 
        text-align: left; 
    }
    th { 
        background: #f0f0f0; 
    }
    .btn {
        display: inline-block;
        padding: 10px 20px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin: 10px 5px;
    }
    .btn:hover {
        background: #0056b3;
    }
    .btn-success {
        background: #28a745;
    }
    .btn-success:hover {
        background: #218838;
    }
</style>

<div style="margin: 20px 0; padding: 15px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h3>Quick Actions:</h3>
    <a href="pages/dr_list.php" class="btn">🔄 Test Dr. List Now</a>
    <a href="pages/dashboard.php" class="btn btn-success">📊 Go to Dashboard</a>
    <a href="setup.php" class="btn">⚙️ Run Full Setup</a>
</div>
