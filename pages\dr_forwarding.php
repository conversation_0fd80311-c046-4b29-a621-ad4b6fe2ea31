<?php
/**
 * Dr. Forwarding - Forward medical bills to doctors
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('dr_forwarding')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Dr. Forwarding';
$message = '';
$messageType = '';

// Handle forwarding submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $action = $_POST['action'];
        
        if ($action === 'forward_bills') {
            $selectedClaims = $_POST['selected_claims'] ?? [];
            $forwardingDate = $_POST['forwarding_date'] ?? date('Y-m-d');
            
            if (empty($selectedClaims)) {
                throw new Exception('Please select at least one claim to forward');
            }
            
            // Get claim details
            $placeholders = str_repeat('?,', count($selectedClaims) - 1) . '?';
            $stmt = $db->prepare("
                SELECT id, claim_no, name, claim_amount, sanctioned_amount 
                FROM medical_claim_register 
                WHERE id IN ($placeholders) AND ph_status IN ('SDN', 'DN', 'CR', 'CO')
            ");
            $stmt->execute($selectedClaims);
            $claims = $stmt->fetchAll();
            
            if (empty($claims)) {
                throw new Exception('No valid claims found for forwarding');
            }
            
            $db->beginTransaction();
            
            // Create forwarding record
            $totalAmount = array_sum(array_column($claims, 'sanctioned_amount'));
            $claimIds = json_encode(array_column($claims, 'id'));
            
            $stmt = $db->prepare("
                INSERT INTO dr_forwarding (forwarding_date, forwarded_by, claim_ids, total_bills, total_amount) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $forwardingDate,
                $_SESSION['user_id'],
                $claimIds,
                count($claims),
                $totalAmount
            ]);
            
            // Update claim status to 'DR'
            $stmt = $db->prepare("
                UPDATE medical_claim_register 
                SET ph_status = 'DR' 
                WHERE id IN ($placeholders)
            ");
            $stmt->execute($selectedClaims);
            
            $db->commit();
            $message = count($claims) . ' claims forwarded to doctor successfully!';
            $messageType = 'success';
            
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Dr forwarding error: " . $e->getMessage());
    }
}

// Get pending claims for forwarding
try {
    $db = getDB();
    $userRole = getUserRole();
    
    // Determine which claims user can forward based on role
    $allowedStatuses = match($userRole) {
        'SDN' => ['SDN'],
        'DN' => ['SDN', 'DN'],
        'CR' => ['SDN', 'DN', 'CR'],
        'CO', 'Admin' => ['SDN', 'DN', 'CR', 'CO'],
        default => []
    };
    
    if (!empty($allowedStatuses)) {
        $statusPlaceholders = str_repeat('?,', count($allowedStatuses) - 1) . '?';
        $stmt = $db->prepare("
            SELECT id, claim_no, employee_number, name, patient_name, disease_name, 
                   dr_name, claim_amount, sanctioned_amount, ph_status, claim_received_date
            FROM medical_claim_register 
            WHERE ph_status IN ($statusPlaceholders)
            ORDER BY claim_received_date DESC
        ");
        $stmt->execute($allowedStatuses);
        $pendingClaims = $stmt->fetchAll();
    } else {
        $pendingClaims = [];
    }
    
    // Get forwarding history
    $stmt = $db->prepare("
        SELECT df.*, u.name as forwarded_by_name
        FROM dr_forwarding df
        LEFT JOIN users u ON df.forwarded_by = u.id
        ORDER BY df.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $forwardingHistory = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("Get forwarding data error: " . $e->getMessage());
    $pendingClaims = [];
    $forwardingHistory = [];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-share me-2"></i>Dr. Forwarding
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Forwarding Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Forward Bills to Doctor</h5>
    </div>
    <div class="card-body">
        <?php if (empty($pendingClaims)): ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">No pending claims available for forwarding.</p>
            </div>
        <?php else: ?>
            <form method="POST" id="forwardingForm">
                <input type="hidden" name="action" value="forward_bills">
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="forwarding_date" class="form-label">Forwarding Date</label>
                        <input type="date" class="form-control" id="forwarding_date" name="forwarding_date" 
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-9 d-flex align-items-end">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                                <i class="fas fa-check-square me-1"></i>Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="selectNone()">
                                <i class="fas fa-square me-1"></i>Select None
                            </button>
                            <button type="submit" class="btn btn-success" onclick="return confirmForwarding()">
                                <i class="fas fa-paper-plane me-1"></i>Forward Selected
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="pendingClaimsTable">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                </th>
                                <th>Claim No</th>
                                <th>Employee</th>
                                <th>Patient</th>
                                <th>Disease</th>
                                <th>Doctor</th>
                                <th>Sanctioned Amount</th>
                                <th>Status</th>
                                <th>Received Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pendingClaims as $claim): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_claims[]" value="<?php echo $claim['id']; ?>" 
                                               class="claim-checkbox">
                                    </td>
                                    <td><strong><?php echo htmlspecialchars($claim['claim_no']); ?></strong></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($claim['name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($claim['employee_number']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($claim['patient_name']); ?></td>
                                    <td><?php echo htmlspecialchars($claim['disease_name']); ?></td>
                                    <td><?php echo htmlspecialchars($claim['dr_name']); ?></td>
                                    <td><?php echo formatCurrency($claim['sanctioned_amount']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $claim['ph_status']; ?></span>
                                    </td>
                                    <td><?php echo formatDate($claim['claim_received_date']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>

<!-- Forwarding History -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Forwarding History</h5>
    </div>
    <div class="card-body">
        <?php if (empty($forwardingHistory)): ?>
            <div class="text-center py-4">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <p class="text-muted">No forwarding history found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Forwarding Date</th>
                            <th>Forwarded By</th>
                            <th>Total Bills</th>
                            <th>Total Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($forwardingHistory as $history): ?>
                            <tr>
                                <td><?php echo formatDate($history['forwarding_date']); ?></td>
                                <td><?php echo htmlspecialchars($history['forwarded_by_name']); ?></td>
                                <td><span class="badge bg-primary"><?php echo $history['total_bills']; ?></span></td>
                                <td><?php echo formatCurrency($history['total_amount']); ?></td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($history['status']) {
                                            'submitted' => 'success',
                                            'received' => 'info',
                                            default => 'warning'
                                        };
                                    ?>">
                                        <?php echo ucfirst($history['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="generateForwardingLetter(<?php echo $history['id']; ?>)">
                                        <i class="fas fa-print me-1"></i>Print Letter
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('.claim-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.claim-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.claim-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function confirmForwarding() {
    const selectedClaims = document.querySelectorAll('.claim-checkbox:checked');
    
    if (selectedClaims.length === 0) {
        alert('Please select at least one claim to forward');
        return false;
    }
    
    return confirm(`Are you sure you want to forward \${selectedClaims.length} claim(s) to the doctor?`);
}

function generateForwardingLetter(forwardingId) {
    window.open('../reports/forwarding_letter.php?id=' + forwardingId, '_blank');
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('pendingClaimsTable')) {
        initDataTable('#pendingClaimsTable', {
            order: [[8, 'desc']],
            pageLength: 50,
            columnDefs: [
                { orderable: false, targets: [0] }
            ]
        });
    }
});

// Update select all checkbox when individual checkboxes change
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('claim-checkbox')) {
        const allCheckboxes = document.querySelectorAll('.claim-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.claim-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        
        if (checkedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
});
</script>
";

include '../includes/footer.php';
?>
