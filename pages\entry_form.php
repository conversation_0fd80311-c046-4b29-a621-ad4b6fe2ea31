<?php
/**
 * Entry Form - Manual claim entry with auto-fill from Claim Master
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('entry_form')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Entry Form';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $claimNo = sanitizeInput($_POST['claim_no'] ?? '');
        $claimReceivedDate = $_POST['claim_received_date'] ?? '';
        $diseaseName = sanitizeInput($_POST['disease_name'] ?? '');
        $drName = sanitizeInput($_POST['dr_name'] ?? '');
        $sanctionedAmount = (float)($_POST['sanctioned_amount'] ?? 0);
        $hospitalStay = $_POST['hospital_stay'] ?? 'no';
        
        // Validation
        if (empty($claimNo) || empty($claimReceivedDate) || empty($diseaseName) || empty($drName) || $sanctionedAmount <= 0) {
            throw new Exception('Please fill all required fields');
        }
        
        // Get claim data from claim_master
        $stmt = $db->prepare("SELECT * FROM claim_master WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        $claimData = $stmt->fetch();
        
        if (!$claimData) {
            throw new Exception('Claim No not available in Claim Master');
        }
        
        // Calculate disallowed amount
        $disallowedAmount = $claimData['claim_amount'] - $sanctionedAmount;
        
        // Check if entry already exists
        $stmt = $db->prepare("SELECT id FROM medical_claim_register WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        
        if ($stmt->fetch()) {
            throw new Exception('This claim has already been entered');
        }
        
        // Insert into medical_claim_register
        $stmt = $db->prepare("
            INSERT INTO medical_claim_register (
                claim_no, employee_number, name, designation, organization, claim_type,
                date_of_application, start_date_of_expense, end_date_of_expense,
                claim_amount, sanctioned_amount, disallowed_amount, status,
                patient_name, relation, treatment_type, claim_received_date,
                disease_name, dr_name, hospital_stay, ph_status, processed_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $claimData['claim_no'],
            $claimData['employee_number'],
            $claimData['name'],
            $claimData['designation'],
            $claimData['organization'],
            $claimData['claim_type'],
            $claimData['date_of_application'],
            $claimData['start_date_of_expense'],
            $claimData['end_date_of_expense'],
            $claimData['claim_amount'],
            $sanctionedAmount,
            $disallowedAmount,
            $claimData['status'],
            $claimData['patient_name'],
            $claimData['relation'],
            $claimData['treatment_type'],
            $claimReceivedDate,
            $diseaseName,
            $drName,
            $hospitalStay,
            getUserRole(), // Set initial status based on user role
            $_SESSION['user_id']
        ]);
        
        $message = 'Medical claim entry created successfully!';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Entry form error: " . $e->getMessage());
    }
}

// Get doctors for dropdown
try {
    $db = getDB();
    $stmt = $db->query("SELECT name FROM doctors_register WHERE status = 'active' ORDER BY name");
    $doctors = $stmt->fetchAll();
} catch (Exception $e) {
    $doctors = [];
    logError("Get doctors error: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-edit me-2"></i>Medical Claim Entry Form
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>New Medical Claim Entry</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="entryForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="claim_no" class="form-label">Claim No <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="claim_no" name="claim_no" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="searchClaim()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="form-text">Enter claim number to auto-fill details</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="claim_received_date" class="form-label">Claim Received Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="claim_received_date" name="claim_received_date" required>
                    </div>
                </div>
            </div>
            
            <!-- Auto-filled fields from Claim Master -->
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="employee_number" class="form-label">Employee Number</label>
                        <input type="text" class="form-control" id="employee_number" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="designation" class="form-label">Designation</label>
                        <input type="text" class="form-control" id="designation" readonly>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="organization" class="form-label">Organization</label>
                        <input type="text" class="form-control" id="organization" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="patient_name" class="form-label">Patient Name</label>
                        <input type="text" class="form-control" id="patient_name" readonly>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="relation" class="form-label">Relation</label>
                        <input type="text" class="form-control" id="relation" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="claim_amount" class="form-label">Claim Amount</label>
                        <input type="number" class="form-control" id="claim_amount" step="0.01" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="date_of_application" class="form-label">Date Of Application</label>
                        <input type="date" class="form-control" id="date_of_application" readonly>
                    </div>
                </div>
            </div>
            
            <!-- User input fields -->
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="disease_name" class="form-label">Disease Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="disease_name" name="disease_name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dr_name" class="form-label">Dr. Name <span class="text-danger">*</span></label>
                        <select class="form-select" id="dr_name" name="dr_name" required>
                            <option value="">Select Doctor</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?php echo htmlspecialchars($doctor['name']); ?>">
                                    <?php echo htmlspecialchars($doctor['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="sanctioned_amount" class="form-label">Sanctioned Amount <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="sanctioned_amount" name="sanctioned_amount" 
                               step="0.01" required onchange="calculateDisallowed()">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="disallowed_amount" class="form-label">Disallowed Amount</label>
                        <input type="number" class="form-control" id="disallowed_amount" step="0.01" readonly>
                        <div class="form-text">Auto-calculated: Claim Amount - Sanctioned Amount</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="hospital_stay" class="form-label">Hospital Stay <span class="text-danger">*</span></label>
                        <select class="form-select" id="hospital_stay" name="hospital_stay" required>
                            <option value="no">No</option>
                            <option value="yes">Yes</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Entry
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear Form
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$additionalJS = "
<script>
function searchClaim() {
    const claimNo = document.getElementById('claim_no').value.trim();
    if (!claimNo) {
        alert('Please enter a claim number');
        return;
    }
    
    showLoading();
    
    fetch('../ajax/get_claim_data.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'claim_no=' + encodeURIComponent(claimNo)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            // Fill form with claim data
            document.getElementById('employee_number').value = data.claim.employee_number || '';
            document.getElementById('name').value = data.claim.name || '';
            document.getElementById('designation').value = data.claim.designation || '';
            document.getElementById('organization').value = data.claim.organization || '';
            document.getElementById('patient_name').value = data.claim.patient_name || '';
            document.getElementById('relation').value = data.claim.relation || '';
            document.getElementById('claim_amount').value = data.claim.claim_amount || '';
            document.getElementById('date_of_application').value = data.claim.date_of_application || '';
        } else {
            alert(data.message || 'Claim No not available');
            clearAutoFillFields();
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('Error fetching claim data');
        clearAutoFillFields();
    });
}

function clearAutoFillFields() {
    const fields = ['employee_number', 'name', 'designation', 'organization', 'patient_name', 'relation', 'claim_amount', 'date_of_application'];
    fields.forEach(field => {
        document.getElementById(field).value = '';
    });
}

function calculateDisallowed() {
    const claimAmount = parseFloat(document.getElementById('claim_amount').value) || 0;
    const sanctionedAmount = parseFloat(document.getElementById('sanctioned_amount').value) || 0;
    const disallowedAmount = claimAmount - sanctionedAmount;
    
    document.getElementById('disallowed_amount').value = disallowedAmount.toFixed(2);
}

function clearForm() {
    document.getElementById('entryForm').reset();
    clearAutoFillFields();
}

// Auto-search when claim number is entered
document.getElementById('claim_no').addEventListener('blur', function() {
    if (this.value.trim()) {
        searchClaim();
    }
});

// Set today's date as default
document.getElementById('claim_received_date').value = new Date().toISOString().split('T')[0];
</script>
";

include '../includes/footer.php';
?>
