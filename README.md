# Medical Bill Management System (MBPHP)

A comprehensive web-based Medical Bill Management System built with PHP and MySQL for managing medical claims, doctor information, and payment processing with role-based access control.

## Features

### Core Functionality
- **Role-Based Access Control (RBAC)**: SDN, DN, CR, CO, Admin roles
- **Medical Claim Processing**: Complete workflow from submission to payment
- **Doctor Management**: Maintain doctor database with contact information
- **Excel Import**: Bulk upload of claim master data via Excel/CSV files
- **Approval Workflow**: Multi-level approval system based on user roles
- **Report Generation**: Various reports for tracking and analysis

### User Interface
- **Responsive Design**: Bootstrap 5 based responsive interface
- **Dark Mode**: Toggle between light and dark themes
- **Dashboard**: Comprehensive overview with statistics and charts
- **Data Tables**: Advanced tables with search, sort, and pagination
- **Modern UI**: Clean and professional interface design

### Technical Features
- **Security**: Password hashing, input sanitization, SQL injection prevention
- **Session Management**: Secure user authentication and session handling
- **Error Handling**: Comprehensive error logging and user feedback
- **Database Optimization**: Indexed queries and optimized database structure

## System Requirements

- **Web Server**: Apache/Nginx with PHP 8.0+
- **Database**: MySQL 8.0+ or MariaDB 10.4+
- **PHP Extensions**: PDO, MySQLi, JSON, File Upload
- **Browser**: Modern web browser with JavaScript enabled

## Installation

### 1. Database Setup
```sql
-- Import the database schema
mysql -u root -p < sql/database_schema.sql
```

### 2. Configuration
Edit `config/database.php` to match your database settings:
```php
private $host = 'localhost';
private $db_name = 'medical_bill_system';
private $username = 'your_username';
private $password = 'your_password';
```

### 3. File Permissions
```bash
chmod 755 assets/uploads/
chmod 755 logs/
```

### 4. Web Server Configuration
Point your web server document root to the project directory.

## Default Login Credentials

- **Username**: ADMIN001
- **Password**: admin123

## User Roles and Permissions

### Admin
- Full system access
- User management
- All reports and configurations

### SDN (Subdivision User)
- Submit medical bills
- View dashboard
- Basic claim entry

### DN (Division User)
- Process SDN submissions
- Forward to CR for large amounts
- Generate section reports

### CR (Circle User)
- Process DN submissions
- Forward to CO for large amounts
- Payment voucher generation

### CO (Corporate User)
- Final approval authority
- Complete system access except user management
- All reporting capabilities

## File Structure

```
MBPHP/
├── config/           # Configuration files
├── includes/         # Header, footer, sidebar includes
├── pages/           # Main application pages
├── classes/         # PHP classes
├── ajax/            # AJAX handlers
├── assets/          # CSS, JS, uploads
├── sql/             # Database schema
└── logs/            # Error logs
```

## Key Features by Module

### Dashboard
- Real-time statistics
- Status breakdown charts
- Recent activities
- Monthly overview

### Dr. List
- Add/Edit/Delete doctors
- Search and pagination
- Contact information management

### Claim Master
- Excel/CSV file upload
- Bulk data processing
- Duplicate handling (by Claim No)
- Data validation

### Entry Form
- Manual claim entry
- Auto-fill from Claim Master
- Doctor selection
- Amount calculations

### Medical Claim Register
- View all processed claims
- Status tracking
- Search and filter options

### Consultation Register
- Multiple consultation entries
- Reconciliation tracking
- Date and amount management

### Dr. Forwarding
- Batch forwarding to doctors
- Letter generation
- Submission tracking

### Dr. Payment Voucher
- Payment calculation (₹20 for hospital stay, ₹10 for consultation)
- Voucher generation
- Payment tracking

### Reports
- Bill-wise section amounts
- Employee-wise section amounts
- Payment vouchers
- Export capabilities

## Security Features

- Password hashing using PHP's password_hash()
- SQL injection prevention with prepared statements
- Input sanitization and validation
- Session security
- Role-based access control
- Error logging without exposing sensitive information

## Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Support

For technical support or feature requests, please contact the development team.

## License

This project is proprietary software developed for internal use.

---

**Developed by Areli**  
**Version**: 1.0.0  
**Last Updated**: <?php echo date('Y-m-d'); ?>
