<?php
/**
 * AJAX handler for getting claim data from claim_master
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$claimNo = sanitizeInput($_POST['claim_no'] ?? '');

if (empty($claimNo)) {
    echo json_encode(['success' => false, 'message' => 'Claim number is required']);
    exit();
}

try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM claim_master WHERE claim_no = ?");
    $stmt->execute([$claimNo]);
    $claim = $stmt->fetch();
    
    if ($claim) {
        echo json_encode([
            'success' => true,
            'claim' => $claim
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Claim No not available in Claim Master'
        ]);
    }
} catch (Exception $e) {
    logError("Get claim data error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching claim data'
    ]);
}
?>
