<?php
/**
 * AJAX handler for getting doctor claims for voucher generation
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$doctorId = (int)($_POST['doctor_id'] ?? 0);
$fromDate = $_POST['from_date'] ?? '';
$toDate = $_POST['to_date'] ?? '';

if ($doctorId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Doctor ID is required']);
    exit();
}

if (empty($fromDate) || empty($toDate)) {
    echo json_encode(['success' => false, 'message' => 'Date range is required']);
    exit();
}

try {
    $db = getDB();
    
    // Get doctor details
    $stmt = $db->prepare("SELECT name FROM doctors_register WHERE id = ?");
    $stmt->execute([$doctorId]);
    $doctor = $stmt->fetch();
    
    if (!$doctor) {
        echo json_encode(['success' => false, 'message' => 'Doctor not found']);
        exit();
    }
    
    // Get claims for this doctor in the date range
    $stmt = $db->prepare("
        SELECT id, claim_no, name, hospital_stay, sanctioned_amount, claim_received_date
        FROM medical_claim_register 
        WHERE dr_name = ? AND ph_status = 'DR' 
        AND claim_received_date BETWEEN ? AND ?
        ORDER BY claim_received_date
    ");
    $stmt->execute([$doctor['name'], $fromDate, $toDate]);
    $claims = $stmt->fetchAll();
    
    // Calculate payment amounts
    $hospitalStayBills = 0;
    $consultationBills = 0;
    $totalAmount = 0;
    
    foreach ($claims as $claim) {
        if ($claim['hospital_stay'] === 'yes') {
            $hospitalStayBills++;
            $totalAmount += 20; // ₹20 per hospital stay bill
        } else {
            $consultationBills++;
            $totalAmount += 10; // ₹10 per consultation bill
        }
    }
    
    $totalBills = count($claims);
    
    echo json_encode([
        'success' => true,
        'doctor_name' => $doctor['name'],
        'total_bills' => $totalBills,
        'hospital_stay_bills' => $hospitalStayBills,
        'consultation_bills' => $consultationBills,
        'total_amount' => $totalAmount,
        'claims' => $claims
    ]);
    
} catch (Exception $e) {
    logError("Get doctor claims error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching doctor claims'
    ]);
}
?>
