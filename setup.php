<?php
/**
 * Setup script for Medical Bill Management System
 * Run this once to initialize the database
 */

// Database configuration - UPDATE THESE VALUES
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'medical_bill_system';

?>
<!DOCTYPE html>
<html>
<head>
    <title>MBPHP Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>

<h1>Medical Bill Management System - Setup</h1>

<?php

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    try {
        // Connect to MySQL server (without database)
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        echo "<p>✓ Connected to MySQL server</p>";

        // Create database if not exists
        $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
        echo "<p>✓ Database '$database' created/verified</p>";

        // Connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create tables
        $tables = [
            "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                employee_no VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                mobile VARCHAR(15),
                designation VARCHAR(100),
                organization VARCHAR(100),
                role ENUM('SDN', 'DN', 'CR', 'CO', 'Admin') NOT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",

            "CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                role_name VARCHAR(50) UNIQUE NOT NULL,
                permissions JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",

            "CREATE TABLE IF NOT EXISTS doctors_register (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                degree VARCHAR(100),
                mobile_no VARCHAR(15),
                email VARCHAR(100),
                address TEXT,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",

            "CREATE TABLE IF NOT EXISTS claim_master (
                id INT AUTO_INCREMENT PRIMARY KEY,
                claim_no VARCHAR(50) UNIQUE NOT NULL,
                employee_number VARCHAR(50) NOT NULL,
                name VARCHAR(100) NOT NULL,
                designation VARCHAR(100),
                organization VARCHAR(100),
                claim_type VARCHAR(50),
                date_of_application DATE,
                start_date_of_expense DATE,
                end_date_of_expense DATE,
                claim_amount DECIMAL(10,2),
                sanctioned_amount DECIMAL(10,2),
                disallowed_amount DECIMAL(10,2),
                status VARCHAR(50),
                patient_name VARCHAR(100),
                relation VARCHAR(50),
                treatment_type VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_claim_no (claim_no),
                INDEX idx_employee_number (employee_number)
            )",

            "CREATE TABLE IF NOT EXISTS medical_claim_register (
                id INT AUTO_INCREMENT PRIMARY KEY,
                claim_no VARCHAR(50) NOT NULL,
                employee_number VARCHAR(50) NOT NULL,
                name VARCHAR(100) NOT NULL,
                designation VARCHAR(100),
                organization VARCHAR(100),
                claim_type VARCHAR(50),
                date_of_application DATE,
                start_date_of_expense DATE,
                end_date_of_expense DATE,
                claim_amount DECIMAL(10,2),
                sanctioned_amount DECIMAL(10,2),
                disallowed_amount DECIMAL(10,2),
                status VARCHAR(50),
                patient_name VARCHAR(100),
                relation VARCHAR(50),
                treatment_type VARCHAR(100),
                claim_received_date DATE,
                disease_name VARCHAR(200),
                dr_name VARCHAR(100),
                hospital_stay ENUM('yes', 'no') DEFAULT 'no',
                ph_status ENUM('SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return') DEFAULT 'SDN',
                paid_salary_month VARCHAR(20),
                dr_voucher_no VARCHAR(50),
                dr_amount DECIMAL(10,2),
                processed_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_claim_no (claim_no),
                INDEX idx_ph_status (ph_status)
            )"
        ];

        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }

        echo "<p>✓ Database tables created successfully</p>";

        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE employee_no = 'ADMIN001'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn() > 0;

        if (!$adminExists) {
            // Create admin user with correct password hash
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (employee_no, password, name, role)
                VALUES ('ADMIN001', ?, 'System Administrator', 'Admin')
            ");
            $stmt->execute([$hashedPassword]);
            echo "<p>✓ Admin user created with correct password hash</p>";
        } else {
            // Update existing admin user password
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE employee_no = 'ADMIN001'");
            $stmt->execute([$hashedPassword]);
            echo "<p>✓ Admin user password updated</p>";
        }

        // Insert default roles
        $roles = [
            ['Admin', '{"dashboard": true, "dr_list": true, "claim_master": true, "entry_form": true, "medical_claim_register": true, "consultation_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true, "user_management": true}'],
            ['SDN', '{"dashboard": true, "entry_form": true, "medical_claim_register": true}'],
            ['DN', '{"dashboard": true, "entry_form": true, "medical_claim_register": true, "dr_forwarding": true, "section_amount": true}'],
            ['CR', '{"dashboard": true, "entry_form": true, "medical_claim_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true}'],
            ['CO', '{"dashboard": true, "dr_list": true, "claim_master": true, "entry_form": true, "medical_claim_register": true, "consultation_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true}']
        ];

        foreach ($roles as $role) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO roles (role_name, permissions) VALUES (?, ?)");
            $stmt->execute($role);
        }
        echo "<p>✓ Default roles created</p>";

        // Insert sample doctors
        $doctors = [
            ['Dr. Rajesh Kumar', 'MBBS, MD', '**********', '<EMAIL>', '123 Medical Street, City'],
            ['Dr. Priya Sharma', 'MBBS, MS', '**********', '<EMAIL>', '456 Health Avenue, City'],
            ['Dr. Amit Patel', 'MBBS, DM', '**********', '<EMAIL>', '789 Care Road, City']
        ];

        foreach ($doctors as $doctor) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO doctors_register (name, degree, mobile_no, email, address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute($doctor);
        }
        echo "<p>✓ Sample doctors added</p>";

        // Create directories
        $dirs = ['assets/uploads', 'logs'];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                echo "<p>✓ Created directory: $dir</p>";
            }
        }

        echo "<div class='success'>";
        echo "<h3>Setup Complete!</h3>";
        echo "<p><strong>Your Medical Bill Management System is ready to use.</strong></p>";
        echo "<p><strong>Login Credentials:</strong></p>";
        echo "<ul>";
        echo "<li>Username: <strong>ADMIN001</strong></li>";
        echo "<li>Password: <strong>admin123</strong></li>";
        echo "</ul>";
        echo "<p><a href='pages/login.php' class='btn'>Go to Login Page</a></p>";
        echo "</div>";

        echo "<div class='warning'>";
        echo "<h4>Important Security Notes:</h4>";
        echo "<ul>";
        echo "<li>Change the default admin password immediately after first login</li>";
        echo "<li>Update database credentials in config/database.php if needed</li>";
        echo "<li>Delete this setup.php file after setup is complete</li>";
        echo "<li>Ensure proper file permissions are set</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>Setup Failed!</h3>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Please check your database configuration and try again.</p>";
        echo "</div>";
    }
} else {
    // Show setup form
    echo "<p>This script will set up the Medical Bill Management System database and create the default admin user.</p>";
    echo "<div class='warning'>";
    echo "<h4>Before proceeding:</h4>";
    echo "<ul>";
    echo "<li>Make sure MySQL server is running</li>";
    echo "<li>Verify database credentials in this file (lines 8-11)</li>";
    echo "<li>Ensure PHP has permission to create directories</li>";
    echo "</ul>";
    echo "</div>";

    echo "<form method='POST'>";
    echo "<button type='submit' name='setup' class='btn'>Start Setup</button>";
    echo "</form>";
}
?>

</body>
</html>
