<?php
/**
 * Setup script for Medical Bill Management System
 * Run this once to initialize the database
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'medical_bill_system';

echo "<h1>Medical Bill Management System - Setup</h1>";

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Connected to MySQL server</p>";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "<p>✓ Database '$database' created/verified</p>";
    
    // Connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute SQL schema
    $sqlFile = __DIR__ . '/sql/database_schema.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Remove the CREATE DATABASE and USE statements since we're already connected
        $sql = preg_replace('/CREATE DATABASE.*?;/', '', $sql);
        $sql = preg_replace('/USE.*?;/', '', $sql);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        echo "<p>✓ Database schema created successfully</p>";
    } else {
        throw new Exception("SQL schema file not found: $sqlFile");
    }
    
    // Verify tables were created
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>✓ Created " . count($tables) . " tables:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE employee_no = 'ADMIN001'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        // Create admin user
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (employee_no, password, name, role) 
            VALUES ('ADMIN001', ?, 'System Administrator', 'Admin')
        ");
        $stmt->execute([$hashedPassword]);
        echo "<p>✓ Admin user created (ADMIN001 / admin123)</p>";
    } else {
        echo "<p>✓ Admin user already exists</p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>Setup Complete!</h3>";
    echo "<p><strong>Your Medical Bill Management System is ready to use.</strong></p>";
    echo "<p><strong>Default Login:</strong></p>";
    echo "<ul>";
    echo "<li>Username: <strong>ADMIN001</strong></li>";
    echo "<li>Password: <strong>admin123</strong></li>";
    echo "</ul>";
    echo "<p><a href='pages/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h4>Important Security Notes:</h4>";
    echo "<ul>";
    echo "<li>Change the default admin password immediately after first login</li>";
    echo "<li>Update database credentials in config/database.php</li>";
    echo "<li>Delete this setup.php file after setup is complete</li>";
    echo "<li>Ensure proper file permissions are set on uploads and logs directories</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>Setup Failed!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>MBPHP Setup</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f8f9fa;
        }
        h1 { 
            color: #2c3e50; 
            border-bottom: 2px solid #3498db; 
            padding-bottom: 10px;
        }
        p { 
            margin: 10px 0; 
        }
        ul { 
            margin: 10px 0; 
        }
        li { 
            margin: 5px 0; 
        }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
