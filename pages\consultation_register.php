<?php
/**
 * Consultation Register - Multiple consultation and reconciliation entries
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('consultation_register')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Consultation Register';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $claimNo = sanitizeInput($_POST['claim_no'] ?? '');
        $consultations = $_POST['consultations'] ?? [];
        $reconciliations = $_POST['reconciliations'] ?? [];
        
        if (empty($claimNo)) {
            throw new Exception('Claim No is required');
        }
        
        // Verify claim exists
        $stmt = $db->prepare("SELECT employee_number, name FROM medical_claim_register WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        $claimData = $stmt->fetch();
        
        if (!$claimData) {
            throw new Exception('Claim No not found in Medical Claim Register');
        }
        
        $db->beginTransaction();
        
        // Delete existing entries for this claim
        $stmt = $db->prepare("DELETE FROM consultation_register WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        
        // Insert consultation entries
        foreach ($consultations as $consultation) {
            if (!empty($consultation['date']) && !empty($consultation['amount'])) {
                $stmt = $db->prepare("
                    INSERT INTO consultation_register (claim_no, employee_number, name, consultation_date, consultation_amount) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $claimNo,
                    $claimData['employee_number'],
                    $claimData['name'],
                    $consultation['date'],
                    $consultation['amount']
                ]);
            }
        }
        
        // Insert reconciliation entries
        foreach ($reconciliations as $reconciliation) {
            if (!empty($reconciliation['date']) && !empty($reconciliation['amount'])) {
                $stmt = $db->prepare("
                    INSERT INTO consultation_register (claim_no, employee_number, name, reconciliation_date, reconciliation_amount) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $claimNo,
                    $claimData['employee_number'],
                    $claimData['name'],
                    $reconciliation['date'],
                    $reconciliation['amount']
                ]);
            }
        }
        
        $db->commit();
        $message = 'Consultation register entries saved successfully!';
        $messageType = 'success';
        
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Consultation register error: " . $e->getMessage());
    }
}

// Get consultation records
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (claim_no LIKE ? OR employee_number LIKE ? OR name LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }
    
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(DISTINCT claim_no) as total FROM consultation_register $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get consultation records grouped by claim
    $stmt = $db->prepare("
        SELECT 
            claim_no,
            employee_number,
            name,
            GROUP_CONCAT(DISTINCT CONCAT(consultation_date, ':', consultation_amount) SEPARATOR '|') as consultations,
            GROUP_CONCAT(DISTINCT CONCAT(reconciliation_date, ':', reconciliation_amount) SEPARATOR '|') as reconciliations,
            SUM(consultation_amount) as total_consultation,
            SUM(reconciliation_amount) as total_reconciliation,
            MAX(created_at) as last_updated
        FROM consultation_register 
        $whereClause 
        GROUP BY claim_no, employee_number, name
        ORDER BY last_updated DESC 
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $consultations = $stmt->fetchAll();
    
    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    logError("Get consultations error: " . $e->getMessage());
    $consultations = [];
    $total = 0;
    $totalPages = 0;
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-stethoscope me-2"></i>Consultation Register
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Entry Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Add Consultation Entry</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="consultationForm">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="claim_no" class="form-label">Claim No <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="claim_no" name="claim_no" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="searchClaim()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="employee_number" class="form-label">Employee Number</label>
                        <input type="text" class="form-control" id="employee_number" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" readonly>
                    </div>
                </div>
            </div>
            
            <!-- Consultation Entries -->
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Consultation Entries</h6>
                    <div id="consultationEntries">
                        <div class="consultation-entry mb-2">
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control" name="consultations[0][date]" placeholder="Date">
                                </div>
                                <div class="col-5">
                                    <input type="number" class="form-control" name="consultations[0][amount]" 
                                           placeholder="Amount" step="0.01">
                                </div>
                                <div class="col-1">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeEntry(this)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addConsultationEntry()">
                        <i class="fas fa-plus me-1"></i>Add Consultation
                    </button>
                </div>
                
                <!-- Reconciliation Entries -->
                <div class="col-md-6">
                    <h6 class="text-primary">Reconciliation Entries</h6>
                    <div id="reconciliationEntries">
                        <div class="reconciliation-entry mb-2">
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control" name="reconciliations[0][date]" placeholder="Date">
                                </div>
                                <div class="col-5">
                                    <input type="number" class="form-control" name="reconciliations[0][amount]" 
                                           placeholder="Amount" step="0.01">
                                </div>
                                <div class="col-1">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeEntry(this)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addReconciliationEntry()">
                        <i class="fas fa-plus me-1"></i>Add Reconciliation
                    </button>
                </div>
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Entries
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear Form
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Consultation Records -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Consultation Records 
            <span class="badge bg-primary"><?php echo number_format($total); ?></span>
        </h5>
        <div class="d-flex gap-2">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="Search..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($consultations)): ?>
            <div class="text-center py-4">
                <i class="fas fa-stethoscope fa-3x text-muted mb-3"></i>
                <p class="text-muted">No consultation records found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="consultationsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Consultations</th>
                            <th>Reconciliations</th>
                            <th>Total Consultation</th>
                            <th>Total Reconciliation</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($consultations as $consultation): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($consultation['claim_no']); ?></strong></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($consultation['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($consultation['employee_number']); ?></small>
                                </td>
                                <td>
                                    <?php 
                                    $consultationList = explode('|', $consultation['consultations']);
                                    foreach ($consultationList as $item) {
                                        if (!empty($item) && $item !== ':') {
                                            list($date, $amount) = explode(':', $item);
                                            if (!empty($date) && !empty($amount)) {
                                                echo "<small class='d-block'>" . formatDate($date) . ": " . formatCurrency($amount) . "</small>";
                                            }
                                        }
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php 
                                    $reconciliationList = explode('|', $consultation['reconciliations']);
                                    foreach ($reconciliationList as $item) {
                                        if (!empty($item) && $item !== ':') {
                                            list($date, $amount) = explode(':', $item);
                                            if (!empty($date) && !empty($amount)) {
                                                echo "<small class='d-block'>" . formatDate($date) . ": " . formatCurrency($amount) . "</small>";
                                            }
                                        }
                                    }
                                    ?>
                                </td>
                                <td><?php echo formatCurrency($consultation['total_consultation']); ?></td>
                                <td><?php echo formatCurrency($consultation['total_reconciliation']); ?></td>
                                <td><?php echo formatDate($consultation['last_updated']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="editConsultation('<?php echo htmlspecialchars($consultation['claim_no']); ?>')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Consultations pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
let consultationIndex = 1;
let reconciliationIndex = 1;

function searchClaim() {
    const claimNo = document.getElementById('claim_no').value.trim();
    if (!claimNo) {
        alert('Please enter a claim number');
        return;
    }
    
    fetch('../ajax/get_claim_basic_data.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'claim_no=' + encodeURIComponent(claimNo)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('employee_number').value = data.employee_number || '';
            document.getElementById('name').value = data.name || '';
        } else {
            alert(data.message || 'Claim not found');
            document.getElementById('employee_number').value = '';
            document.getElementById('name').value = '';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error fetching claim data');
    });
}

function addConsultationEntry() {
    const container = document.getElementById('consultationEntries');
    const newEntry = document.createElement('div');
    newEntry.className = 'consultation-entry mb-2';
    newEntry.innerHTML = `
        <div class='row'>
            <div class='col-6'>
                <input type='date' class='form-control' name='consultations[\${consultationIndex}][date]' placeholder='Date'>
            </div>
            <div class='col-5'>
                <input type='number' class='form-control' name='consultations[\${consultationIndex}][amount]' 
                       placeholder='Amount' step='0.01'>
            </div>
            <div class='col-1'>
                <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeEntry(this)'>
                    <i class='fas fa-times'></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newEntry);
    consultationIndex++;
}

function addReconciliationEntry() {
    const container = document.getElementById('reconciliationEntries');
    const newEntry = document.createElement('div');
    newEntry.className = 'reconciliation-entry mb-2';
    newEntry.innerHTML = `
        <div class='row'>
            <div class='col-6'>
                <input type='date' class='form-control' name='reconciliations[\${reconciliationIndex}][date]' placeholder='Date'>
            </div>
            <div class='col-5'>
                <input type='number' class='form-control' name='reconciliations[\${reconciliationIndex}][amount]' 
                       placeholder='Amount' step='0.01'>
            </div>
            <div class='col-1'>
                <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeEntry(this)'>
                    <i class='fas fa-times'></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newEntry);
    reconciliationIndex++;
}

function removeEntry(button) {
    const entry = button.closest('.consultation-entry, .reconciliation-entry');
    if (entry.parentNode.children.length > 1) {
        entry.remove();
    }
}

function clearForm() {
    document.getElementById('consultationForm').reset();
    document.getElementById('employee_number').value = '';
    document.getElementById('name').value = '';
    
    // Reset to single entries
    document.getElementById('consultationEntries').innerHTML = `
        <div class='consultation-entry mb-2'>
            <div class='row'>
                <div class='col-6'>
                    <input type='date' class='form-control' name='consultations[0][date]' placeholder='Date'>
                </div>
                <div class='col-5'>
                    <input type='number' class='form-control' name='consultations[0][amount]' 
                           placeholder='Amount' step='0.01'>
                </div>
                <div class='col-1'>
                    <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeEntry(this)'>
                        <i class='fas fa-times'></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('reconciliationEntries').innerHTML = `
        <div class='reconciliation-entry mb-2'>
            <div class='row'>
                <div class='col-6'>
                    <input type='date' class='form-control' name='reconciliations[0][date]' placeholder='Date'>
                </div>
                <div class='col-5'>
                    <input type='number' class='form-control' name='reconciliations[0][amount]' 
                           placeholder='Amount' step='0.01'>
                </div>
                <div class='col-1'>
                    <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeEntry(this)'>
                        <i class='fas fa-times'></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    consultationIndex = 1;
    reconciliationIndex = 1;
}

function editConsultation(claimNo) {
    document.getElementById('claim_no').value = claimNo;
    searchClaim();
    document.getElementById('claim_no').scrollIntoView();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('consultationsTable')) {
        initDataTable('#consultationsTable', {
            order: [[6, 'desc']],
            columnDefs: [
                { orderable: false, targets: [7] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
