<?php
/**
 * Sidebar navigation menu
 */

$currentPage = basename($_SERVER['PHP_SELF']);

// Define menu items with permissions
$menuItems = [
    [
        'title' => 'Dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'url' => 'dashboard.php',
        'permission' => 'dashboard'
    ],
    [
        'title' => 'Dr. List',
        'icon' => 'fas fa-user-md',
        'url' => 'dr_list.php',
        'permission' => 'dr_list'
    ],
    [
        'title' => 'Claim Master',
        'icon' => 'fas fa-file-medical',
        'url' => 'claim_master.php',
        'permission' => 'claim_master'
    ],
    [
        'title' => 'Entry Form',
        'icon' => 'fas fa-edit',
        'url' => 'entry_form.php',
        'permission' => 'entry_form'
    ],
    [
        'title' => 'Medical Claim Register',
        'icon' => 'fas fa-clipboard-list',
        'url' => 'medical_claim_register.php',
        'permission' => 'medical_claim_register'
    ],
    [
        'title' => 'Consultation Register',
        'icon' => 'fas fa-stethoscope',
        'url' => 'consultation_register.php',
        'permission' => 'consultation_register'
    ],
    [
        'title' => 'Dr. Forwarding',
        'icon' => 'fas fa-share',
        'url' => 'dr_forwarding.php',
        'permission' => 'dr_forwarding'
    ],
    [
        'title' => 'Dr. Payment Voucher',
        'icon' => 'fas fa-receipt',
        'url' => 'dr_payment_voucher.php',
        'permission' => 'dr_payment_voucher'
    ],
    [
        'title' => 'Section Amount',
        'icon' => 'fas fa-calculator',
        'url' => 'section_amount.php',
        'permission' => 'section_amount'
    ],
    [
        'title' => 'Bill wise Section Amount',
        'icon' => 'fas fa-chart-bar',
        'url' => 'bill_wise_section.php',
        'permission' => 'reports'
    ],
    [
        'title' => 'Employee wise Section Amount',
        'icon' => 'fas fa-chart-pie',
        'url' => 'employee_wise_section.php',
        'permission' => 'reports'
    ]
];

// Add admin-only menu items
if (getUserRole() === 'Admin') {
    $menuItems[] = [
        'title' => 'User Management',
        'icon' => 'fas fa-users-cog',
        'url' => 'user_management.php',
        'permission' => 'user_management'
    ];
}
?>

<div class="nav flex-column">
    <div class="nav-header p-3">
        <h6 class="text-muted text-uppercase fw-bold">Navigation</h6>
    </div>
    
    <?php foreach ($menuItems as $item): ?>
        <?php if (hasPermission($item['permission'])): ?>
            <a href="<?php echo $item['url']; ?>" 
               class="nav-link <?php echo ($currentPage === $item['url']) ? 'active' : ''; ?>">
                <i class="<?php echo $item['icon']; ?> me-2"></i>
                <?php echo $item['title']; ?>
            </a>
        <?php endif; ?>
    <?php endforeach; ?>
    
    <div class="nav-divider my-3"></div>
    
    <div class="nav-header px-3">
        <h6 class="text-muted text-uppercase fw-bold">Quick Actions</h6>
    </div>
    
    <?php if (hasPermission('entry_form')): ?>
        <a href="entry_form.php" class="nav-link">
            <i class="fas fa-plus-circle me-2"></i>
            New Entry
        </a>
    <?php endif; ?>
    
    <?php if (hasPermission('dr_forwarding')): ?>
        <a href="dr_forwarding.php" class="nav-link">
            <i class="fas fa-paper-plane me-2"></i>
            Forward to Doctor
        </a>
    <?php endif; ?>
    
    <?php if (hasPermission('reports')): ?>
        <div class="dropdown">
            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-chart-line me-2"></i>
                Reports
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="bill_wise_section.php">
                    <i class="fas fa-chart-bar me-2"></i>Bill wise Report
                </a></li>
                <li><a class="dropdown-item" href="employee_wise_section.php">
                    <i class="fas fa-chart-pie me-2"></i>Employee wise Report
                </a></li>
                <li><a class="dropdown-item" href="dr_payment_voucher.php">
                    <i class="fas fa-receipt me-2"></i>Payment Voucher
                </a></li>
            </ul>
        </div>
    <?php endif; ?>
</div>

<style>
.nav-header {
    border-bottom: 1px solid #dee2e6;
}

[data-bs-theme="dark"] .nav-header {
    border-bottom-color: #404040;
}

.nav-divider {
    height: 1px;
    background-color: #dee2e6;
    margin: 0 20px;
}

[data-bs-theme="dark"] .nav-divider {
    background-color: #404040;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--secondary-color);
    color: white;
}

.nav-link.dropdown-toggle::after {
    float: right;
    margin-top: 8px;
}
</style>
