<?php
/**
 * Employee wise Section Amount Report
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('reports')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Employee wise Section Amount Report';

// Get filter parameters
$fromDate = $_GET['from_date'] ?? date('Y-m-01'); // First day of current month
$toDate = $_GET['to_date'] ?? date('Y-m-d'); // Today
$organization = $_GET['organization'] ?? '';
$search = $_GET['search'] ?? '';

// Get report data
try {
    $db = getDB();
    
    $whereClause = "WHERE mcr.claim_received_date BETWEEN ? AND ?";
    $params = [$fromDate, $toDate];
    
    if (!empty($organization)) {
        $whereClause .= " AND mcr.organization = ?";
        $params[] = $organization;
    }
    
    if (!empty($search)) {
        $whereClause .= " AND (mcr.employee_number LIKE ? OR mcr.name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm]);
    }
    
    // Get employee-wise aggregated data
    $stmt = $db->prepare("
        SELECT 
            mcr.employee_number,
            mcr.name,
            mcr.designation,
            mcr.organization,
            COUNT(*) as total_claims,
            SUM(mcr.claim_amount) as total_claim_amount,
            SUM(mcr.sanctioned_amount) as total_sanctioned_amount,
            SUM(mcr.disallowed_amount) as total_disallowed_amount,
            SUM(CASE WHEN mcr.hospital_stay = 'yes' THEN 1 ELSE 0 END) as hospital_stay_claims,
            SUM(CASE WHEN mcr.hospital_stay = 'no' THEN 1 ELSE 0 END) as consultation_claims,
            SUM(CASE WHEN mcr.ph_status = 'Paid' THEN mcr.sanctioned_amount ELSE 0 END) as paid_amount,
            SUM(CASE WHEN mcr.ph_status = 'Paid' THEN 1 ELSE 0 END) as paid_claims,
            MIN(mcr.claim_received_date) as first_claim_date,
            MAX(mcr.claim_received_date) as last_claim_date
        FROM medical_claim_register mcr
        $whereClause
        GROUP BY mcr.employee_number, mcr.name, mcr.designation, mcr.organization
        ORDER BY total_sanctioned_amount DESC, mcr.name
    ");
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
    
    // Calculate summary statistics
    $summary = [
        'total_employees' => count($employees),
        'total_claims' => array_sum(array_column($employees, 'total_claims')),
        'total_claim_amount' => array_sum(array_column($employees, 'total_claim_amount')),
        'total_sanctioned_amount' => array_sum(array_column($employees, 'total_sanctioned_amount')),
        'total_disallowed_amount' => array_sum(array_column($employees, 'total_disallowed_amount')),
        'total_paid_amount' => array_sum(array_column($employees, 'paid_amount')),
        'total_paid_claims' => array_sum(array_column($employees, 'paid_claims'))
    ];
    
    // Get organizations for filter
    $stmt = $db->query("SELECT DISTINCT organization FROM medical_claim_register WHERE organization IS NOT NULL ORDER BY organization");
    $organizations = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Top employees by amount
    $topEmployees = array_slice($employees, 0, 10);
    
} catch (Exception $e) {
    logError("Employee wise report error: " . $e->getMessage());
    $employees = [];
    $organizations = [];
    $topEmployees = [];
    $summary = [
        'total_employees' => 0,
        'total_claims' => 0,
        'total_claim_amount' => 0,
        'total_sanctioned_amount' => 0,
        'total_disallowed_amount' => 0,
        'total_paid_amount' => 0,
        'total_paid_claims' => 0
    ];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-pie me-2"></i>Employee wise Section Amount Report
        </h1>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Report Filters</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="from_date" class="form-label">From Date</label>
                <input type="date" class="form-control" id="from_date" name="from_date" value="<?php echo $fromDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="to_date" class="form-label">To Date</label>
                <input type="date" class="form-control" id="to_date" name="to_date" value="<?php echo $toDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="organization" class="form-label">Organization</label>
                <select class="form-select" id="organization" name="organization">
                    <option value="">All Organizations</option>
                    <?php foreach ($organizations as $org): ?>
                        <option value="<?php echo htmlspecialchars($org); ?>" <?php echo $organization === $org ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($org); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="search" class="form-label">Search Employee</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Employee No, Name..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Generate Report
                </button>
                <a href="employee_wise_section.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
                <button type="button" class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download me-1"></i>Export Excel
                </button>
                <button type="button" class="btn btn-info" onclick="printReport()">
                    <i class="fas fa-print me-1"></i>Print Report
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number"><?php echo number_format($summary['total_employees']); ?></div>
            <div class="stats-label">Total Employees</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #3498db, #2980b9);">
            <div class="stats-number"><?php echo number_format($summary['total_claims']); ?></div>
            <div class="stats-label">Total Claims</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_sanctioned_amount']); ?></div>
            <div class="stats-label">Total Sanctioned</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_disallowed_amount']); ?></div>
            <div class="stats-label">Total Disallowed</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
            <div class="stats-number"><?php echo formatCurrency($summary['total_paid_amount']); ?></div>
            <div class="stats-label">Total Paid</div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
            <div class="stats-number"><?php echo number_format($summary['total_paid_claims']); ?></div>
            <div class="stats-label">Paid Claims</div>
        </div>
    </div>
</div>

<!-- Top Employees Chart -->
<?php if (!empty($topEmployees)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top 10 Employees by Sanctioned Amount</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach (array_slice($topEmployees, 0, 5) as $index => $employee): ?>
                <div class="col-md-2 mb-3">
                    <div class="text-center">
                        <div class="badge bg-<?php echo ['primary', 'success', 'info', 'warning', 'secondary'][$index]; ?> fs-6 mb-2">
                            #<?php echo $index + 1; ?>
                        </div>
                        <h6 class="mb-1"><?php echo htmlspecialchars($employee['name']); ?></h6>
                        <small class="text-muted d-block"><?php echo htmlspecialchars($employee['employee_number']); ?></small>
                        <strong class="text-primary"><?php echo formatCurrency($employee['total_sanctioned_amount']); ?></strong>
                        <small class="text-muted d-block"><?php echo $employee['total_claims']; ?> claims</small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Employee Details Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>Employee wise Details 
            <span class="badge bg-primary"><?php echo number_format($summary['total_employees']); ?></span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($employees)): ?>
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">No employees found for the selected criteria.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="employeesTable">
                    <thead>
                        <tr>
                            <th>Employee No</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Organization</th>
                            <th>Total Claims</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned Amount</th>
                            <th>Disallowed Amount</th>
                            <th>Paid Amount</th>
                            <th>Hospital/Consultation</th>
                            <th>First Claim</th>
                            <th>Last Claim</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees as $employee): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($employee['employee_number']); ?></strong></td>
                                <td><strong><?php echo htmlspecialchars($employee['name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($employee['designation']); ?></td>
                                <td><?php echo htmlspecialchars($employee['organization']); ?></td>
                                <td><span class="badge bg-primary"><?php echo $employee['total_claims']; ?></span></td>
                                <td><?php echo formatCurrency($employee['total_claim_amount']); ?></td>
                                <td><strong><?php echo formatCurrency($employee['total_sanctioned_amount']); ?></strong></td>
                                <td><?php echo formatCurrency($employee['total_disallowed_amount']); ?></td>
                                <td><?php echo formatCurrency($employee['paid_amount']); ?></td>
                                <td>
                                    <small class="d-block">H: <?php echo $employee['hospital_stay_claims']; ?></small>
                                    <small class="d-block">C: <?php echo $employee['consultation_claims']; ?></small>
                                </td>
                                <td><?php echo formatDate($employee['first_claim_date']); ?></td>
                                <td><?php echo formatDate($employee['last_claim_date']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <th colspan="4">Total</th>
                            <th><?php echo number_format($summary['total_claims']); ?></th>
                            <th><?php echo formatCurrency($summary['total_claim_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_sanctioned_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_disallowed_amount']); ?></th>
                            <th><?php echo formatCurrency($summary['total_paid_amount']); ?></th>
                            <th colspan="3"><?php echo number_format($summary['total_employees']); ?> Employees</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '../ajax/export_employee_wise_report.php?' + params.toString();
}

function printReport() {
    window.print();
}

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('employeesTable')) {
        initDataTable('#employeesTable', {
            order: [[6, 'desc']], // Sort by sanctioned amount
            pageLength: 50,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            columnDefs: [
                { type: 'currency', targets: [5, 6, 7, 8] }
            ],
            footerCallback: function (row, data, start, end, display) {
                // Keep the footer totals visible
            }
        });
    }
});

// Print styles
const printStyles = `
    <style>
        @media print {
            .sidebar, .navbar, .btn, .card-header .btn-group { display: none !important; }
            .main-content { margin-left: 0 !important; }
            .card { border: 1px solid #000 !important; }
            .table { font-size: 11px !important; }
            .stats-card { border: 1px solid #000 !important; margin-bottom: 10px !important; }
            .badge { background: #000 !important; color: white !important; }
        }
    </style>
`;
document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
";

include '../includes/footer.php';
?>
