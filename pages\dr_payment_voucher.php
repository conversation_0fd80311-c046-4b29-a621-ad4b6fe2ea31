<?php
/**
 * Dr. Payment Voucher - Generate payment vouchers for doctors
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('dr_payment_voucher')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Dr. Payment Voucher';
$message = '';
$messageType = '';

// Handle voucher generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $action = $_POST['action'];
        
        if ($action === 'generate_voucher') {
            $doctorId = (int)($_POST['doctor_id'] ?? 0);
            $voucherDate = $_POST['voucher_date'] ?? date('Y-m-d');
            $fromDate = $_POST['from_date'] ?? '';
            $toDate = $_POST['to_date'] ?? '';
            
            if ($doctorId <= 0) {
                throw new Exception('Please select a doctor');
            }
            
            if (empty($fromDate) || empty($toDate)) {
                throw new Exception('Please select date range');
            }
            
            // Get doctor details
            $stmt = $db->prepare("SELECT * FROM doctors_register WHERE id = ?");
            $stmt->execute([$doctorId]);
            $doctor = $stmt->fetch();
            
            if (!$doctor) {
                throw new Exception('Doctor not found');
            }
            
            // Get claims for this doctor in the date range
            $stmt = $db->prepare("
                SELECT * FROM medical_claim_register 
                WHERE dr_name = ? AND ph_status = 'DR' 
                AND claim_received_date BETWEEN ? AND ?
                ORDER BY claim_received_date
            ");
            $stmt->execute([$doctor['name'], $fromDate, $toDate]);
            $claims = $stmt->fetchAll();
            
            if (empty($claims)) {
                throw new Exception('No claims found for this doctor in the selected date range');
            }
            
            // Calculate payment amounts
            $hospitalStayBills = 0;
            $consultationBills = 0;
            $totalAmount = 0;
            
            foreach ($claims as $claim) {
                if ($claim['hospital_stay'] === 'yes') {
                    $hospitalStayBills++;
                    $totalAmount += 20; // ₹20 per hospital stay bill
                } else {
                    $consultationBills++;
                    $totalAmount += 10; // ₹10 per consultation bill
                }
            }
            
            $totalBills = count($claims);
            
            // Generate voucher number
            $voucherNo = generateVoucherNo();
            
            $db->beginTransaction();
            
            // Insert voucher record
            $stmt = $db->prepare("
                INSERT INTO dr_payment_voucher (
                    voucher_no, voucher_date, doctor_id, total_bills, 
                    hospital_stay_bills, consultation_bills, total_amount, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $voucherNo,
                $voucherDate,
                $doctorId,
                $totalBills,
                $hospitalStayBills,
                $consultationBills,
                $totalAmount,
                $_SESSION['user_id']
            ]);
            
            $voucherId = $db->lastInsertId();
            
            // Update claims with voucher information
            $claimIds = array_column($claims, 'id');
            $placeholders = str_repeat('?,', count($claimIds) - 1) . '?';
            
            $stmt = $db->prepare("
                UPDATE medical_claim_register 
                SET dr_voucher_no = ?, dr_amount = ?, ph_status = 'In Payment' 
                WHERE id IN ($placeholders)
            ");
            $params = [$voucherNo];
            foreach ($claims as $claim) {
                $params[] = $claim['hospital_stay'] === 'yes' ? 20 : 10;
            }
            $params = array_merge($params, $claimIds);
            $stmt->execute($params);
            
            $db->commit();
            
            $message = "Payment voucher generated successfully! Voucher No: $voucherNo";
            $messageType = 'success';
            
            // Redirect to print voucher
            header("Location: ../reports/payment_voucher.php?id=$voucherId");
            exit();
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Dr payment voucher error: " . $e->getMessage());
    }
}

// Get doctors
try {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM doctors_register WHERE status = 'active' ORDER BY name");
    $doctors = $stmt->fetchAll();
    
    // Get voucher history
    $stmt = $db->prepare("
        SELECT dpv.*, dr.name as doctor_name, u.name as created_by_name
        FROM dr_payment_voucher dpv
        LEFT JOIN doctors_register dr ON dpv.doctor_id = dr.id
        LEFT JOIN users u ON dpv.created_by = u.id
        ORDER BY dpv.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $vouchers = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("Get voucher data error: " . $e->getMessage());
    $doctors = [];
    $vouchers = [];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-receipt me-2"></i>Dr. Payment Voucher
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Voucher Generation Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Generate Payment Voucher</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="voucherForm">
            <input type="hidden" name="action" value="generate_voucher">
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="doctor_id" class="form-label">Select Doctor <span class="text-danger">*</span></label>
                        <select class="form-select" id="doctor_id" name="doctor_id" required onchange="checkClaims()">
                            <option value="">Choose Doctor</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?php echo $doctor['id']; ?>">
                                    <?php echo htmlspecialchars($doctor['name']); ?> - <?php echo htmlspecialchars($doctor['degree']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="voucher_date" class="form-label">Voucher Date</label>
                        <input type="date" class="form-control" id="voucher_date" name="voucher_date" 
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="from_date" class="form-label">From Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="from_date" name="from_date" required onchange="checkClaims()">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="to_date" class="form-label">To Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="to_date" name="to_date" required onchange="checkClaims()">
                    </div>
                </div>
            </div>
            
            <!-- Claims Preview -->
            <div id="claimsPreview" class="mt-3" style="display: none;">
                <h6 class="text-primary">Claims Preview</h6>
                <div id="claimsData"></div>
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary" id="generateBtn" disabled>
                    <i class="fas fa-receipt me-2"></i>Generate Voucher
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Payment Calculation Info -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Payment Calculation</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="alert alert-info">
                    <h6><i class="fas fa-bed me-2"></i>Hospital Stay Bills</h6>
                    <p class="mb-0">₹20 per bill for claims with hospital stay</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="alert alert-success">
                    <h6><i class="fas fa-stethoscope me-2"></i>Consultation Bills</h6>
                    <p class="mb-0">₹10 per bill for consultation only claims</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Voucher History -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Payment Vouchers</h5>
    </div>
    <div class="card-body">
        <?php if (empty($vouchers)): ?>
            <div class="text-center py-4">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <p class="text-muted">No payment vouchers found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="vouchersTable">
                    <thead>
                        <tr>
                            <th>Voucher No</th>
                            <th>Doctor</th>
                            <th>Voucher Date</th>
                            <th>Total Bills</th>
                            <th>Hospital Stay</th>
                            <th>Consultation</th>
                            <th>Total Amount</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($vouchers as $voucher): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($voucher['voucher_no']); ?></strong></td>
                                <td><?php echo htmlspecialchars($voucher['doctor_name']); ?></td>
                                <td><?php echo formatDate($voucher['voucher_date']); ?></td>
                                <td><span class="badge bg-primary"><?php echo $voucher['total_bills']; ?></span></td>
                                <td><span class="badge bg-info"><?php echo $voucher['hospital_stay_bills']; ?></span></td>
                                <td><span class="badge bg-success"><?php echo $voucher['consultation_bills']; ?></span></td>
                                <td><strong><?php echo formatCurrency($voucher['total_amount']); ?></strong></td>
                                <td><?php echo htmlspecialchars($voucher['created_by_name']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="printVoucher(<?php echo $voucher['id']; ?>)" title="Print Voucher">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewVoucher(<?php echo $voucher['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
function checkClaims() {
    const doctorId = document.getElementById('doctor_id').value;
    const fromDate = document.getElementById('from_date').value;
    const toDate = document.getElementById('to_date').value;
    
    if (!doctorId || !fromDate || !toDate) {
        document.getElementById('claimsPreview').style.display = 'none';
        document.getElementById('generateBtn').disabled = true;
        return;
    }
    
    if (new Date(fromDate) > new Date(toDate)) {
        alert('From date cannot be greater than to date');
        return;
    }
    
    showLoading();
    
    fetch('../ajax/get_doctor_claims.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `doctor_id=\${doctorId}&from_date=\${fromDate}&to_date=\${toDate}`
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            displayClaimsPreview(data);
            document.getElementById('generateBtn').disabled = data.total_bills === 0;
        } else {
            alert(data.message || 'Error fetching claims');
            document.getElementById('claimsPreview').style.display = 'none';
            document.getElementById('generateBtn').disabled = true;
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('Error fetching claims');
        document.getElementById('claimsPreview').style.display = 'none';
        document.getElementById('generateBtn').disabled = true;
    });
}

function displayClaimsPreview(data) {
    const preview = document.getElementById('claimsPreview');
    const claimsData = document.getElementById('claimsData');
    
    if (data.total_bills === 0) {
        claimsData.innerHTML = '<div class=\"alert alert-warning\">No claims found for the selected doctor and date range.</div>';
    } else {
        claimsData.innerHTML = `
            <div class='row'>
                <div class='col-md-3'>
                    <div class='card bg-primary text-white'>
                        <div class='card-body text-center'>
                            <h4>\${data.total_bills}</h4>
                            <small>Total Bills</small>
                        </div>
                    </div>
                </div>
                <div class='col-md-3'>
                    <div class='card bg-info text-white'>
                        <div class='card-body text-center'>
                            <h4>\${data.hospital_stay_bills}</h4>
                            <small>Hospital Stay</small>
                        </div>
                    </div>
                </div>
                <div class='col-md-3'>
                    <div class='card bg-success text-white'>
                        <div class='card-body text-center'>
                            <h4>\${data.consultation_bills}</h4>
                            <small>Consultation</small>
                        </div>
                    </div>
                </div>
                <div class='col-md-3'>
                    <div class='card bg-warning text-white'>
                        <div class='card-body text-center'>
                            <h4>₹\${data.total_amount}</h4>
                            <small>Total Amount</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    preview.style.display = 'block';
}

function clearForm() {
    document.getElementById('voucherForm').reset();
    document.getElementById('voucher_date').value = new Date().toISOString().split('T')[0];
    document.getElementById('claimsPreview').style.display = 'none';
    document.getElementById('generateBtn').disabled = true;
}

function printVoucher(voucherId) {
    window.open('../reports/payment_voucher.php?id=' + voucherId, '_blank');
}

function viewVoucher(voucherId) {
    window.open('../reports/payment_voucher.php?id=' + voucherId + '&view=1', '_blank');
}

// Set default date range (current month)
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    document.getElementById('from_date').value = firstDay.toISOString().split('T')[0];
    document.getElementById('to_date').value = lastDay.toISOString().split('T')[0];
    
    // Initialize DataTable
    if (document.getElementById('vouchersTable')) {
        initDataTable('#vouchersTable', {
            order: [[2, 'desc']],
            columnDefs: [
                { orderable: false, targets: [8] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
