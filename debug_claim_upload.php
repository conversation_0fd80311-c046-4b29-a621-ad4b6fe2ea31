<?php
/**
 * Debug version of claim_master.php to show INSERT queries
 */

require_once 'config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('claim_master')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Debug Claim Master Upload';
$message = '';
$messageType = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    echo "<h1>Debug: File Upload Process</h1>";
    
    try {
        $file = $_FILES['excel_file'];
        
        echo "<h2>Step 1: File Information</h2>";
        echo "<ul>";
        echo "<li>File name: " . htmlspecialchars($file['name']) . "</li>";
        echo "<li>File size: " . number_format($file['size']) . " bytes</li>";
        echo "<li>File type: " . htmlspecialchars($file['type']) . "</li>";
        echo "<li>Temp name: " . htmlspecialchars($file['tmp_name']) . "</li>";
        echo "<li>Error code: " . $file['error'] . "</li>";
        echo "</ul>";
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $file['error']);
        }
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        echo "<p><strong>File extension:</strong> $fileExtension</p>";
        
        if (!in_array($fileExtension, ['xls', 'xlsx', 'csv'])) {
            throw new Exception('Only Excel (.xls, .xlsx) and CSV files are allowed');
        }
        
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size exceeds maximum limit of ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }
        
        // Create upload directory if not exists
        if (!is_dir(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
            echo "<p>Created upload directory: " . UPLOAD_DIR . "</p>";
        }
        
        // Move uploaded file
        $fileName = 'claim_master_' . date('Y-m-d_H-i-s') . '.' . $fileExtension;
        $filePath = UPLOAD_DIR . $fileName;
        
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('Failed to save uploaded file');
        }
        
        echo "<p><strong>File saved to:</strong> $filePath</p>";
        
        // Process the file
        $db = getDB();
        $recordsProcessed = 0;
        $recordsUpdated = 0;
        
        echo "<h2>Step 2: Processing CSV File</h2>";
        
        if ($fileExtension === 'csv') {
            $handle = fopen($filePath, 'r');
            $header = fgetcsv($handle); // Get header row
            
            echo "<p><strong>Headers found:</strong></p>";
            echo "<ol>";
            foreach ($header as $index => $columnName) {
                echo "<li>[$index] " . htmlspecialchars($columnName) . "</li>";
            }
            echo "</ol>";
            
            // Map header columns to indices for flexible column mapping
            $columnMap = [];
            foreach ($header as $index => $columnName) {
                $columnName = strtolower(trim($columnName));
                $columnName = str_replace([' ', '_'], '', $columnName); // Remove spaces and underscores
                $columnMap[$columnName] = $index;
            }
            
            echo "<p><strong>Column mapping:</strong></p>";
            echo "<pre>" . print_r($columnMap, true) . "</pre>";
            
            // Define expected column mappings (flexible)
            $expectedColumns = [
                'claimno' => ['claimno', 'claim_no', 'claimnumber'],
                'employeenumber' => ['employeenumber', 'employee_number', 'empno', 'emp_no'],
                'name' => ['name', 'employeename', 'employee_name'],
                'designation' => ['designation', 'post', 'position'],
                'organization' => ['organization', 'dept', 'department', 'office'],
                'claimtype' => ['claimtype', 'claim_type', 'type'],
                'dateofapplication' => ['dateofapplication', 'date_of_application', 'applicationdate', 'application_date'],
                'startdateofexpense' => ['startdateofexpense', 'start_date_of_expense', 'startdate', 'start_date'],
                'enddateofexpense' => ['enddateofexpense', 'end_date_of_expense', 'enddate', 'end_date'],
                'claimamount' => ['claimamount', 'claim_amount', 'amount'],
                'sanctionedamount' => ['sanctionedamount', 'sanctioned_amount', 'sanctioned'],
                'disallowedamount' => ['disallowedamount', 'disallowed_amount', 'disallowed'],
                'status' => ['status', 'claimstatus', 'claim_status'],
                'patientname' => ['patientname', 'patient_name', 'patient'],
                'relation' => ['relation', 'relationship'],
                'treatmenttype' => ['treatmenttype', 'treatment_type', 'treatmemttype', 'treatnemt_type'] // Handle typo
            ];
            
            // Find column indices
            $columns = [];
            foreach ($expectedColumns as $key => $variations) {
                $columns[$key] = null;
                foreach ($variations as $variation) {
                    if (isset($columnMap[$variation])) {
                        $columns[$key] = $columnMap[$variation];
                        break;
                    }
                }
            }
            
            echo "<p><strong>Column indices found:</strong></p>";
            echo "<pre>" . print_r($columns, true) . "</pre>";
            
            // Function to convert date format
            function convertDate($dateStr) {
                if (empty($dateStr)) return null;
                
                // Handle different date formats
                $dateStr = trim($dateStr);
                
                // Try different date formats
                $formats = [
                    'd-M-y',    // 17-May-24
                    'd-M-Y',    // 17-May-2024
                    'd/m/Y',    // 17/05/2024
                    'd-m-Y',    // 17-05-2024
                    'Y-m-d',    // 2024-05-17
                    'm/d/Y',    // 05/17/2024
                ];
                
                foreach ($formats as $format) {
                    $date = DateTime::createFromFormat($format, $dateStr);
                    if ($date !== false) {
                        return $date->format('Y-m-d');
                    }
                }
                
                // Try strtotime as fallback
                $timestamp = strtotime($dateStr);
                if ($timestamp !== false) {
                    return date('Y-m-d', $timestamp);
                }
                
                return null;
            }
            
            echo "<h2>Step 3: Processing Data Rows</h2>";
            $rowNumber = 1;
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                echo "<h3>Row $rowNumber:</h3>";
                echo "<p><strong>Raw data:</strong></p>";
                echo "<ol>";
                foreach ($data as $index => $value) {
                    echo "<li>[$index] " . htmlspecialchars($value) . "</li>";
                }
                echo "</ol>";
                
                echo "<p><strong>Data count:</strong> " . count($data) . " (minimum required: 10)</p>";
                
                if (count($data) >= 10) { // Ensure minimum required columns
                    $claimNo = isset($columns['claimno']) ? trim($data[$columns['claimno']]) : '';
                    
                    echo "<p><strong>Claim No extracted:</strong> '$claimNo'</p>";
                    
                    if (!empty($claimNo)) {
                        // Extract data using column mapping
                        $employeeNumber = isset($columns['employeenumber']) ? trim($data[$columns['employeenumber']]) : '';
                        $name = isset($columns['name']) ? trim($data[$columns['name']]) : '';
                        $designation = isset($columns['designation']) ? trim($data[$columns['designation']]) : '';
                        $organization = isset($columns['organization']) ? trim($data[$columns['organization']]) : '';
                        $claimType = isset($columns['claimtype']) ? trim($data[$columns['claimtype']]) : '';
                        $dateOfApplication = isset($columns['dateofapplication']) ? convertDate($data[$columns['dateofapplication']]) : null;
                        $startDate = isset($columns['startdateofexpense']) ? convertDate($data[$columns['startdateofexpense']]) : null;
                        $endDate = isset($columns['enddateofexpense']) ? convertDate($data[$columns['enddateofexpense']]) : null;
                        $claimAmount = isset($columns['claimamount']) && is_numeric($data[$columns['claimamount']]) ? $data[$columns['claimamount']] : 0;
                        $sanctionedAmount = isset($columns['sanctionedamount']) && is_numeric($data[$columns['sanctionedamount']]) ? $data[$columns['sanctionedamount']] : 0;
                        $disallowedAmount = isset($columns['disallowedamount']) && is_numeric($data[$columns['disallowedamount']]) ? $data[$columns['disallowedamount']] : 0;
                        $status = isset($columns['status']) ? trim($data[$columns['status']]) : '';
                        $patientName = isset($columns['patientname']) ? trim($data[$columns['patientname']]) : '';
                        $relation = isset($columns['relation']) ? trim($data[$columns['relation']]) : '';
                        $treatmentType = isset($columns['treatmenttype']) ? trim($data[$columns['treatmenttype']]) : '';
                        
                        echo "<p><strong>Extracted values:</strong></p>";
                        echo "<ul>";
                        echo "<li>Claim No: '$claimNo'</li>";
                        echo "<li>Employee Number: '$employeeNumber'</li>";
                        echo "<li>Name: '$name'</li>";
                        echo "<li>Designation: '$designation'</li>";
                        echo "<li>Organization: '$organization'</li>";
                        echo "<li>Claim Type: '$claimType'</li>";
                        echo "<li>Date of Application: '$dateOfApplication'</li>";
                        echo "<li>Start Date: '$startDate'</li>";
                        echo "<li>End Date: '$endDate'</li>";
                        echo "<li>Claim Amount: '$claimAmount'</li>";
                        echo "<li>Sanctioned Amount: '$sanctionedAmount'</li>";
                        echo "<li>Disallowed Amount: '$disallowedAmount'</li>";
                        echo "<li>Status: '$status'</li>";
                        echo "<li>Patient Name: '$patientName'</li>";
                        echo "<li>Relation: '$relation'</li>";
                        echo "<li>Treatment Type: '$treatmentType'</li>";
                        echo "</ul>";
                        
                        // Check if claim exists
                        $checkStmt = $db->prepare("SELECT id FROM claim_master WHERE claim_no = ?");
                        $checkStmt->execute([$claimNo]);
                        $existingRecord = $checkStmt->fetch();
                        
                        if ($existingRecord) {
                            echo "<p style='color: orange;'>⚠️ Record exists, will UPDATE</p>";
                            
                            $sql = "UPDATE claim_master SET 
                                employee_number = ?, name = ?, designation = ?, organization = ?, 
                                claim_type = ?, date_of_application = ?, start_date_of_expense = ?, 
                                end_date_of_expense = ?, claim_amount = ?, sanctioned_amount = ?, 
                                disallowed_amount = ?, status = ?, patient_name = ?, relation = ?, 
                                treatment_type = ?, updated_at = NOW()
                                WHERE claim_no = ?";
                            
                            $params = [
                                $employeeNumber, $name, $designation, $organization, $claimType,
                                $dateOfApplication, $startDate, $endDate, $claimAmount, 
                                $sanctionedAmount, $disallowedAmount, $status, $patientName, 
                                $relation, $treatmentType, $claimNo
                            ];
                            
                            echo "<p><strong>UPDATE SQL:</strong></p>";
                            echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($sql) . "</pre>";
                            echo "<p><strong>Parameters:</strong></p>";
                            echo "<pre>" . print_r($params, true) . "</pre>";
                            
                            $stmt = $db->prepare($sql);
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                echo "<p style='color: green;'>✅ UPDATE successful</p>";
                                $recordsUpdated++;
                            } else {
                                echo "<p style='color: red;'>❌ UPDATE failed</p>";
                                echo "<p>Error: " . print_r($stmt->errorInfo(), true) . "</p>";
                            }
                        } else {
                            echo "<p style='color: blue;'>ℹ️ New record, will INSERT</p>";
                            
                            $sql = "INSERT INTO claim_master (
                                claim_no, employee_number, name, designation, organization, 
                                claim_type, date_of_application, start_date_of_expense, 
                                end_date_of_expense, claim_amount, sanctioned_amount, 
                                disallowed_amount, status, patient_name, relation, treatment_type
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            
                            $params = [
                                $claimNo, $employeeNumber, $name, $designation, $organization, 
                                $claimType, $dateOfApplication, $startDate, $endDate, 
                                $claimAmount, $sanctionedAmount, $disallowedAmount, $status, 
                                $patientName, $relation, $treatmentType
                            ];
                            
                            echo "<p><strong>INSERT SQL:</strong></p>";
                            echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($sql) . "</pre>";
                            echo "<p><strong>Parameters:</strong></p>";
                            echo "<pre>" . print_r($params, true) . "</pre>";
                            
                            $stmt = $db->prepare($sql);
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                echo "<p style='color: green;'>✅ INSERT successful</p>";
                                $recordsProcessed++;
                            } else {
                                echo "<p style='color: red;'>❌ INSERT failed</p>";
                                echo "<p>Error: " . print_r($stmt->errorInfo(), true) . "</p>";
                            }
                        }
                    } else {
                        echo "<p style='color: red;'>❌ Empty claim number, skipping row</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Insufficient columns, skipping row</p>";
                }
                
                $rowNumber++;
                echo "<hr>";
            }
            fclose($handle);
        }
        
        echo "<h2>Final Results:</h2>";
        echo "<p><strong>Records Processed:</strong> $recordsProcessed</p>";
        echo "<p><strong>Records Updated:</strong> $recordsUpdated</p>";
        
        $message = "File uploaded successfully! Processed: $recordsProcessed records, Updated: $recordsUpdated records.";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        echo "<p style='color: red;'><strong>Exception:</strong> " . $e->getMessage() . "</p>";
        logError("Claim Master upload error: " . $e->getMessage());
    }
    
    echo "<div style='margin: 20px 0; padding: 15px; background: " . ($messageType === 'success' ? '#d4edda' : '#f8d7da') . "; border-radius: 5px;'>";
    echo "<h3>Final Message:</h3>";
    echo "<p>$message</p>";
    echo "</div>";
    
    echo "<p><a href='pages/claim_master.php'>← Back to Claim Master</a></p>";
    
} else {
    // Show upload form
    include '../includes/header.php';
    ?>
    
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-bug me-2"></i>Debug Claim Master Upload
            </h1>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload File for Debug</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="excel_file" class="form-label">Select CSV File</label>
                    <input type="file" class="form-control" id="excel_file" name="excel_file" 
                           accept=".csv" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload & Debug
                </button>
            </form>
        </div>
    </div>
    
    <?php
    include '../includes/footer.php';
}
?>
