<?php
/**
 * Section Amount - Manual entry of section amounts
 */

require_once '../config/config.php';

// Check authentication and permissions
checkAuth();
if (!hasPermission('section_amount')) {
    header('Location: dashboard.php');
    exit();
}

$pageTitle = 'Section Amount';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $claimNo = sanitizeInput($_POST['claim_no'] ?? '');
        $sanctionedAmount = (float)($_POST['sanctioned_amount'] ?? 0);
        
        if (empty($claimNo)) {
            throw new Exception('Claim No is required');
        }
        
        if ($sanctionedAmount <= 0) {
            throw new Exception('Sanctioned Amount must be greater than 0');
        }
        
        // Get claim data from medical_claim_register
        $stmt = $db->prepare("SELECT * FROM medical_claim_register WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        $claimData = $stmt->fetch();
        
        if (!$claimData) {
            throw new Exception('Claim No not found in Medical Claim Register');
        }
        
        // Calculate disallowed amount
        $disallowedAmount = $claimData['claim_amount'] - $sanctionedAmount;
        
        // Check if entry already exists
        $stmt = $db->prepare("SELECT id FROM section_amount WHERE claim_no = ?");
        $stmt->execute([$claimNo]);
        
        if ($stmt->fetch()) {
            // Update existing entry
            $stmt = $db->prepare("
                UPDATE section_amount 
                SET sanctioned_amount = ?, disallowed_amount = ? 
                WHERE claim_no = ?
            ");
            $stmt->execute([$sanctionedAmount, $disallowedAmount, $claimNo]);
            $message = 'Section amount updated successfully!';
        } else {
            // Insert new entry
            $stmt = $db->prepare("
                INSERT INTO section_amount (claim_no, employee_number, name, claim_amount, sanctioned_amount, disallowed_amount, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $claimData['claim_no'],
                $claimData['employee_number'],
                $claimData['name'],
                $claimData['claim_amount'],
                $sanctionedAmount,
                $disallowedAmount,
                $_SESSION['user_id']
            ]);
            $message = 'Section amount entry created successfully!';
        }
        
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Section amount error: " . $e->getMessage());
    }
}

// Get section amount records
try {
    $db = getDB();
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (sa.claim_no LIKE ? OR sa.employee_number LIKE ? OR sa.name LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }
    
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM section_amount sa $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get records
    $stmt = $db->prepare("
        SELECT sa.*, u.name as created_by_name
        FROM section_amount sa
        LEFT JOIN users u ON sa.created_by = u.id
        $whereClause 
        ORDER BY sa.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $sectionAmounts = $stmt->fetchAll();
    
    $totalPages = ceil($total / $limit);
    
    // Get summary statistics
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_entries,
            SUM(claim_amount) as total_claim_amount,
            SUM(sanctioned_amount) as total_sanctioned_amount,
            SUM(disallowed_amount) as total_disallowed_amount
        FROM section_amount
    ");
    $summary = $stmt->fetch();
    
} catch (Exception $e) {
    logError("Get section amounts error: " . $e->getMessage());
    $sectionAmounts = [];
    $total = 0;
    $totalPages = 0;
    $summary = [
        'total_entries' => 0,
        'total_claim_amount' => 0,
        'total_sanctioned_amount' => 0,
        'total_disallowed_amount' => 0
    ];
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-calculator me-2"></i>Section Amount Management
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo number_format($summary['total_entries']); ?></div>
                    <div class="stats-label">Total Entries</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-list fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #3498db, #2980b9);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($summary['total_claim_amount']); ?></div>
                    <div class="stats-label">Total Claim Amount</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-rupee-sign fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($summary['total_sanctioned_amount']); ?></div>
                    <div class="stats-label">Total Sanctioned</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($summary['total_disallowed_amount']); ?></div>
                    <div class="stats-label">Total Disallowed</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Entry Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Add Section Amount Entry</h5>
    </div>
    <div class="card-body">
        <form method="POST" id="sectionAmountForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="claim_no" class="form-label">Claim No <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="claim_no" name="claim_no" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="searchClaim()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="employee_number" class="form-label">Employee Number</label>
                        <input type="text" class="form-control" id="employee_number" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" readonly>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="claim_amount" class="form-label">Claim Amount</label>
                        <input type="number" class="form-control" id="claim_amount" step="0.01" readonly>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="sanctioned_amount" class="form-label">Sanctioned Amount <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="sanctioned_amount" name="sanctioned_amount" 
                               step="0.01" required onchange="calculateDisallowed()">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="disallowed_amount" class="form-label">Disallowed Amount</label>
                        <input type="number" class="form-control" id="disallowed_amount" step="0.01" readonly>
                        <div class="form-text">Auto-calculated</div>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-2"></i>Save Entry
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                    <i class="fas fa-times me-2"></i>Clear Form
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Section Amount Records -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Section Amount Records 
            <span class="badge bg-primary"><?php echo number_format($total); ?></span>
        </h5>
        <div class="d-flex gap-2">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="Search..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <button type="button" class="btn btn-success" onclick="exportData()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($sectionAmounts)): ?>
            <div class="text-center py-4">
                <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                <p class="text-muted">No section amount records found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="sectionAmountsTable">
                    <thead>
                        <tr>
                            <th>Claim No</th>
                            <th>Employee</th>
                            <th>Name</th>
                            <th>Claim Amount</th>
                            <th>Sanctioned Amount</th>
                            <th>Disallowed Amount</th>
                            <th>Created By</th>
                            <th>Created Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sectionAmounts as $record): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($record['claim_no']); ?></strong></td>
                                <td><?php echo htmlspecialchars($record['employee_number']); ?></td>
                                <td><?php echo htmlspecialchars($record['name']); ?></td>
                                <td><?php echo formatCurrency($record['claim_amount']); ?></td>
                                <td><?php echo formatCurrency($record['sanctioned_amount']); ?></td>
                                <td><?php echo formatCurrency($record['disallowed_amount']); ?></td>
                                <td><?php echo htmlspecialchars($record['created_by_name']); ?></td>
                                <td><?php echo formatDate($record['created_at']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="editRecord('<?php echo htmlspecialchars($record['claim_no']); ?>', <?php echo $record['sanctioned_amount']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Section amounts pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$additionalJS = "
<script>
function searchClaim() {
    const claimNo = document.getElementById('claim_no').value.trim();
    if (!claimNo) {
        alert('Please enter a claim number');
        return;
    }
    
    fetch('../ajax/get_claim_basic_data.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'claim_no=' + encodeURIComponent(claimNo)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('employee_number').value = data.employee_number || '';
            document.getElementById('name').value = data.name || '';
            
            // Get claim amount
            return fetch('../ajax/get_claim_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'claim_no=' + encodeURIComponent(claimNo)
            });
        } else {
            throw new Error(data.message || 'Claim not found');
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('claim_amount').value = data.claim.claim_amount || '';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message || 'Error fetching claim data');
        clearAutoFillFields();
    });
}

function clearAutoFillFields() {
    document.getElementById('employee_number').value = '';
    document.getElementById('name').value = '';
    document.getElementById('claim_amount').value = '';
}

function calculateDisallowed() {
    const claimAmount = parseFloat(document.getElementById('claim_amount').value) || 0;
    const sanctionedAmount = parseFloat(document.getElementById('sanctioned_amount').value) || 0;
    const disallowedAmount = claimAmount - sanctionedAmount;
    
    document.getElementById('disallowed_amount').value = disallowedAmount.toFixed(2);
}

function clearForm() {
    document.getElementById('sectionAmountForm').reset();
    clearAutoFillFields();
}

function editRecord(claimNo, sanctionedAmount) {
    document.getElementById('claim_no').value = claimNo;
    document.getElementById('sanctioned_amount').value = sanctionedAmount;
    searchClaim();
    document.getElementById('claim_no').scrollIntoView();
}

function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '../ajax/export_section_amounts.php?' + params.toString();
}

// Auto-search when claim number is entered
document.getElementById('claim_no').addEventListener('blur', function() {
    if (this.value.trim()) {
        searchClaim();
    }
});

// Initialize DataTable
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('sectionAmountsTable')) {
        initDataTable('#sectionAmountsTable', {
            order: [[7, 'desc']],
            columnDefs: [
                { orderable: false, targets: [8] }
            ]
        });
    }
});
</script>
";

include '../includes/footer.php';
?>
