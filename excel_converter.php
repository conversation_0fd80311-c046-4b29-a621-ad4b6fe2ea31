<?php
/**
 * Excel to CSV Converter - Alternative solution for Excel files
 */

require_once 'config/config.php';

// Check authentication
checkAuth();

$pageTitle = 'Excel to CSV Converter';
$message = '';
$messageType = '';

// Handle file conversion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    try {
        $file = $_FILES['excel_file'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error');
        }
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if ($fileExtension !== 'xlsx') {
            throw new Exception('Only .xlsx files are supported for conversion');
        }
        
        // Create upload directory if not exists
        if (!is_dir(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
        }
        
        // Move uploaded file
        $fileName = 'excel_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = UPLOAD_DIR . $fileName;
        
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('Failed to save uploaded file');
        }
        
        // Check if ZipArchive is available
        if (!class_exists('ZipArchive')) {
            throw new Exception('ZipArchive extension is not available. Please enable php_zip extension in your PHP configuration.');
        }
        
        // Convert Excel to CSV
        $zip = new ZipArchive();
        if ($zip->open($filePath) === TRUE) {
            // Get the shared strings
            $sharedStrings = [];
            if (($sharedStringsXML = $zip->getFromName('xl/sharedStrings.xml')) !== false) {
                $xml = simplexml_load_string($sharedStringsXML);
                if ($xml && isset($xml->si)) {
                    foreach ($xml->si as $si) {
                        $sharedStrings[] = (string)$si->t;
                    }
                }
            }
            
            // Get the worksheet data
            if (($worksheetXML = $zip->getFromName('xl/worksheets/sheet1.xml')) !== false) {
                $xml = simplexml_load_string($worksheetXML);
                $csvData = [];
                
                if ($xml && isset($xml->sheetData->row)) {
                    foreach ($xml->sheetData->row as $row) {
                        $rowData = [];
                        $maxCol = 0;
                        
                        // First pass to find max column
                        foreach ($row->c as $cell) {
                            $cellRef = (string)$cell['r'];
                            preg_match('/([A-Z]+)/', $cellRef, $matches);
                            if (isset($matches[1])) {
                                $colIndex = 0;
                                $colStr = $matches[1];
                                for ($i = 0; $i < strlen($colStr); $i++) {
                                    $colIndex = $colIndex * 26 + (ord($colStr[$i]) - ord('A') + 1);
                                }
                                $maxCol = max($maxCol, $colIndex - 1);
                            }
                        }
                        
                        // Initialize row with empty values
                        $rowData = array_fill(0, $maxCol + 1, '');
                        
                        // Fill actual values
                        foreach ($row->c as $cell) {
                            $cellRef = (string)$cell['r'];
                            preg_match('/([A-Z]+)/', $cellRef, $matches);
                            if (isset($matches[1])) {
                                $colIndex = 0;
                                $colStr = $matches[1];
                                for ($i = 0; $i < strlen($colStr); $i++) {
                                    $colIndex = $colIndex * 26 + (ord($colStr[$i]) - ord('A') + 1);
                                }
                                $colIndex--; // Convert to 0-based
                                
                                $value = '';
                                if (isset($cell->v)) {
                                    $cellValue = (string)$cell->v;
                                    // Check if it's a shared string
                                    if (isset($cell['t']) && $cell['t'] == 's') {
                                        $value = isset($sharedStrings[$cellValue]) ? $sharedStrings[$cellValue] : '';
                                    } else {
                                        $value = $cellValue;
                                    }
                                }
                                $rowData[$colIndex] = $value;
                            }
                        }
                        $csvData[] = $rowData;
                    }
                }
                
                // Generate CSV file
                $csvFileName = 'converted_' . date('Y-m-d_H-i-s') . '.csv';
                $csvFilePath = UPLOAD_DIR . $csvFileName;
                
                $csvFile = fopen($csvFilePath, 'w');
                foreach ($csvData as $row) {
                    fputcsv($csvFile, $row);
                }
                fclose($csvFile);
                
                $zip->close();
                
                // Clean up Excel file
                unlink($filePath);
                
                $message = "Excel file converted successfully! <a href='" . UPLOAD_DIR . $csvFileName . "' download>Download CSV file</a>";
                $messageType = 'success';
                
            } else {
                throw new Exception('Unable to read worksheet data from Excel file');
            }
        } else {
            throw new Exception('Unable to open Excel file');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
        logError("Excel converter error: " . $e->getMessage());
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-exchange-alt me-2"></i>Excel to CSV Converter
        </h1>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Instructions -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Enable Excel Support</h5>
    </div>
    <div class="card-body">
        <h6>Option 1: Enable ZipArchive Extension (Recommended)</h6>
        <ol>
            <li>Open your <code>php.ini</code> file (usually in <code>C:\xampp\php\php.ini</code>)</li>
            <li>Find the line <code>;extension=zip</code></li>
            <li>Remove the semicolon to make it <code>extension=zip</code></li>
            <li>Restart Apache server</li>
            <li>Excel files will work directly in Claim Master</li>
        </ol>
        
        <h6>Option 2: Convert Excel to CSV (Current Solution)</h6>
        <ol>
            <li>Upload your Excel file using the form below</li>
            <li>Download the converted CSV file</li>
            <li>Upload the CSV file in Claim Master</li>
        </ol>
        
        <h6>Option 3: Manual Conversion in Excel</h6>
        <ol>
            <li>Open your Excel file</li>
            <li>Go to <strong>File → Save As</strong></li>
            <li>Choose <strong>CSV (Comma delimited) (*.csv)</strong></li>
            <li>Save the file</li>
            <li>Upload the CSV file in Claim Master</li>
        </ol>
    </div>
</div>

<!-- Converter Form -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Convert Excel to CSV</h5>
    </div>
    <div class="card-body">
        <?php if (class_exists('ZipArchive')): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Good news!</strong> ZipArchive extension is available. You can use Excel files directly in Claim Master.
                <a href="pages/claim_master.php" class="btn btn-sm btn-success ms-2">Go to Claim Master</a>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>ZipArchive extension not available.</strong> Use this converter or enable the extension.
            </div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label">Select Excel File (.xlsx)</label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file"
                               accept=".xlsx" required>
                        <div class="form-text">
                            Only .xlsx files are supported for conversion
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-exchange-alt me-2"></i>Convert to CSV
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Recent Conversions -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Conversions</h5>
    </div>
    <div class="card-body">
        <?php
        $uploadDir = UPLOAD_DIR;
        if (is_dir($uploadDir)) {
            $files = glob($uploadDir . 'converted_*.csv');
            if (!empty($files)) {
                // Sort by modification time, newest first
                usort($files, function($a, $b) {
                    return filemtime($b) - filemtime($a);
                });
                
                echo "<div class='table-responsive'>";
                echo "<table class='table table-sm'>";
                echo "<thead><tr><th>File Name</th><th>Size</th><th>Created</th><th>Action</th></tr></thead>";
                echo "<tbody>";
                
                foreach (array_slice($files, 0, 10) as $file) {
                    $fileName = basename($file);
                    $fileSize = number_format(filesize($file) / 1024, 2) . ' KB';
                    $fileTime = date('Y-m-d H:i:s', filemtime($file));
                    
                    echo "<tr>";
                    echo "<td>$fileName</td>";
                    echo "<td>$fileSize</td>";
                    echo "<td>$fileTime</td>";
                    echo "<td><a href='$file' download class='btn btn-sm btn-outline-primary'><i class='fas fa-download'></i> Download</a></td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table></div>";
            } else {
                echo "<p class='text-muted'>No converted files found.</p>";
            }
        } else {
            echo "<p class='text-muted'>Upload directory not found.</p>";
        }
        ?>
    </div>
</div>

<style>
    .alert a {
        text-decoration: none;
    }
    .alert a:hover {
        text-decoration: underline;
    }
</style>

<?php include 'includes/footer.php'; ?>
