<?php
/**
 * User class for authentication and user management
 */

require_once __DIR__ . '/../config/config.php';

class User {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Authenticate user login
     */
    public function login($employee_no, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, employee_no, password, name, role, status 
                FROM users 
                WHERE employee_no = ? AND status = 'active'
            ");
            $stmt->execute([$employee_no]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['employee_no'] = $user['employee_no'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_role'] = $user['role'];
                
                // Log login activity
                $this->logActivity($user['id'], 'login', 'User logged in');
                
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            logError("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'User logged out');
        }
        
        session_destroy();
        return true;
    }
    
    /**
     * Create new user
     */
    public function createUser($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO users (employee_no, password, name, email, mobile, designation, organization, role) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            return $stmt->execute([
                $data['employee_no'],
                $hashedPassword,
                $data['name'],
                $data['email'] ?? null,
                $data['mobile'] ?? null,
                $data['designation'] ?? null,
                $data['organization'] ?? null,
                $data['role']
            ]);
        } catch (Exception $e) {
            logError("Create user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update user
     */
    public function updateUser($id, $data) {
        try {
            $sql = "UPDATE users SET name = ?, email = ?, mobile = ?, designation = ?, organization = ?, role = ?";
            $params = [
                $data['name'],
                $data['email'] ?? null,
                $data['mobile'] ?? null,
                $data['designation'] ?? null,
                $data['organization'] ?? null,
                $data['role']
            ];
            
            if (!empty($data['password'])) {
                $sql .= ", password = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
        } catch (Exception $e) {
            logError("Update user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (Exception $e) {
            logError("Get user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all users with pagination
     */
    public function getUsers($page = 1, $limit = RECORDS_PER_PAGE, $search = '') {
        try {
            $offset = ($page - 1) * $limit;
            
            $whereClause = '';
            $params = [];
            
            if (!empty($search)) {
                $whereClause = "WHERE name LIKE ? OR employee_no LIKE ? OR role LIKE ?";
                $searchTerm = "%$search%";
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }
            
            // Get total count
            $countStmt = $this->db->prepare("SELECT COUNT(*) as total FROM users $whereClause");
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // Get users
            $stmt = $this->db->prepare("
                SELECT id, employee_no, name, email, mobile, designation, organization, role, status, created_at 
                FROM users 
                $whereClause 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $users = $stmt->fetchAll();
            
            return [
                'users' => $users,
                'total' => $total,
                'pages' => ceil($total / $limit),
                'current_page' => $page
            ];
        } catch (Exception $e) {
            logError("Get users error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete user
     */
    public function deleteUser($id) {
        try {
            $stmt = $this->db->prepare("UPDATE users SET status = 'inactive' WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (Exception $e) {
            logError("Delete user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log user activity
     */
    private function logActivity($userId, $action, $description) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO user_activity_log (user_id, action, description, ip_address, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $userId,
                $action,
                $description,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            // Don't throw error for logging failures
            logError("Activity log error: " . $e->getMessage());
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($userId, $oldPassword, $newPassword) {
        try {
            // Verify old password
            $stmt = $this->db->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($oldPassword, $user['password'])) {
                return false;
            }
            
            // Update password
            $stmt = $this->db->prepare("UPDATE users SET password = ? WHERE id = ?");
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            return $stmt->execute([$hashedPassword, $userId]);
        } catch (Exception $e) {
            logError("Change password error: " . $e->getMessage());
            return false;
        }
    }
}
?>
