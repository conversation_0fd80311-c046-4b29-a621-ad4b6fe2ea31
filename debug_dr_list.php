<?php
/**
 * Debug version of dr_list.php to find the exact issue
 */

require_once 'config/config.php';

echo "<h1>Debug Dr. List Query</h1>";

// Check authentication
if (!isLoggedIn()) {
    echo "<p style='color: red;'>❌ Not logged in!</p>";
    echo "<p><a href='pages/login.php'>Login here</a></p>";
    exit();
}

echo "<p style='color: green;'>✅ User logged in: " . $_SESSION['user_name'] . " (" . $_SESSION['user_role'] . ")</p>";

// Check permissions
if (!hasPermission('dr_list')) {
    echo "<p style='color: red;'>❌ No permission for dr_list!</p>";
    echo "<p>User role: " . getUserRole() . "</p>";
    exit();
}

echo "<p style='color: green;'>✅ User has dr_list permission</p>";

try {
    $db = getDB();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test the exact query from dr_list.php
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = RECORDS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    echo "<h2>Query Parameters:</h2>";
    echo "<ul>";
    echo "<li>Search: '" . htmlspecialchars($search) . "'</li>";
    echo "<li>Page: $page</li>";
    echo "<li>Limit: $limit</li>";
    echo "<li>Offset: $offset</li>";
    echo "</ul>";
    
    $whereClause = "WHERE (status = 'active' OR status IS NULL)";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (name LIKE ? OR degree LIKE ? OR mobile_no LIKE ? OR email LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }
    
    echo "<h2>SQL Query:</h2>";
    echo "<p><strong>WHERE Clause:</strong> $whereClause</p>";
    echo "<p><strong>Parameters:</strong> " . json_encode($params) . "</p>";
    
    // Test count query
    echo "<h2>Testing Count Query:</h2>";
    $countSQL = "SELECT COUNT(*) as total FROM doctors_register $whereClause";
    echo "<p><strong>Count SQL:</strong> $countSQL</p>";
    
    $countStmt = $db->prepare($countSQL);
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    echo "<p style='color: " . ($total > 0 ? 'green' : 'red') . ";'><strong>Total Count: $total</strong></p>";
    
    // Test main query
    echo "<h2>Testing Main Query:</h2>";
    $mainSQL = "SELECT * FROM doctors_register $whereClause ORDER BY name LIMIT ? OFFSET ?";
    echo "<p><strong>Main SQL:</strong> $mainSQL</p>";
    
    $mainParams = $params;
    $mainParams[] = $limit;
    $mainParams[] = $offset;
    
    echo "<p><strong>Main Parameters:</strong> " . json_encode($mainParams) . "</p>";
    
    $stmt = $db->prepare($mainSQL);
    $stmt->execute($mainParams);
    $doctors = $stmt->fetchAll();
    
    echo "<p style='color: " . (count($doctors) > 0 ? 'green' : 'red') . ";'><strong>Doctors Found: " . count($doctors) . "</strong></p>";
    
    if (!empty($doctors)) {
        echo "<h2>Doctors Data:</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Degree</th><th>Mobile</th><th>Email</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($doctors as $doctor) {
            echo "<tr>";
            echo "<td>" . $doctor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($doctor['name']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['degree']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['mobile_no']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['email']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $totalPages = ceil($total / $limit);
    echo "<p><strong>Total Pages: $totalPages</strong></p>";
    
    // Test without any filters
    echo "<h2>Testing Without Filters:</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM doctors_register");
    $totalAll = $stmt->fetch()['total'];
    echo "<p><strong>Total doctors (no filter): $totalAll</strong></p>";
    
    $stmt = $db->query("SELECT * FROM doctors_register ORDER BY name LIMIT 10");
    $allDoctors = $stmt->fetchAll();
    echo "<p><strong>Sample doctors (no filter): " . count($allDoctors) . "</strong></p>";
    
    if (!empty($allDoctors)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Degree</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($allDoctors as $doctor) {
            echo "<tr>";
            echo "<td>" . $doctor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($doctor['name']) . "</td>";
            echo "<td>" . htmlspecialchars($doctor['degree']) . "</td>";
            echo "<td style='color: " . ($doctor['status'] === 'active' ? 'green' : 'red') . ";'>" . htmlspecialchars($doctor['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check RECORDS_PER_PAGE constant
    echo "<h2>Constants Check:</h2>";
    echo "<p><strong>RECORDS_PER_PAGE:</strong> " . (defined('RECORDS_PER_PAGE') ? RECORDS_PER_PAGE : 'NOT DEFINED') . "</p>";
    
    // Check if there are any errors in logs
    echo "<h2>Error Log Check:</h2>";
    $logFile = 'logs/error.log';
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $recentLogs = array_slice(explode("\n", $logContent), -10);
        echo "<p><strong>Recent log entries:</strong></p>";
        echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(implode("\n", $recentLogs));
        echo "</pre>";
    } else {
        echo "<p>No error log file found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Exception caught:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        margin: 20px; 
        background: #f8f9fa; 
    }
    h1, h2 { 
        color: #333; 
        border-bottom: 2px solid #007bff; 
        padding-bottom: 5px; 
    }
    table { 
        background: white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        margin: 10px 0;
    }
    th, td { 
        padding: 8px; 
        text-align: left; 
    }
    th { 
        background: #f0f0f0; 
    }
    .btn {
        display: inline-block;
        padding: 10px 20px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin: 10px 5px;
    }
    .btn:hover {
        background: #0056b3;
    }
</style>

<div style="margin: 20px 0;">
    <a href="pages/dr_list.php" class="btn">🔄 Try Dr. List Again</a>
    <a href="fix_doctors.php" class="btn">🔧 Run Doctor Fix</a>
    <a href="pages/dashboard.php" class="btn">📊 Dashboard</a>
</div>
