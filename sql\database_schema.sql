-- Medical Bill Management System Database Schema
-- Created for MBPHP Project

CREATE DATABASE IF NOT EXISTS medical_bill_system;
USE medical_bill_system;

-- Users table for authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_no VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(100),
    mobile VARCHAR(15),
    designation VARCHAR(100),
    organization VARCHAR(100),
    role ENUM('SDN', 'DN', 'CR', 'CO', 'Admin') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Roles table for RBAC
CREATE TABLE roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Doctors Register table
CREATE TABLE doctors_register (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    degree VARCHAR(100),
    mobile_no VARCHAR(15),
    email VARCHAR(100),
    address TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Claim Master table (main data source)
CREATE TABLE claim_master (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_no VARCHAR(50) UNIQUE NOT NULL,
    employee_number VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    designation VARCHAR(100),
    organization VARCHAR(100),
    claim_type VARCHAR(50),
    date_of_application DATE,
    start_date_of_expense DATE,
    end_date_of_expense DATE,
    claim_amount DECIMAL(10,2),
    sanctioned_amount DECIMAL(10,2),
    disallowed_amount DECIMAL(10,2),
    status VARCHAR(50),
    patient_name VARCHAR(100),
    relation VARCHAR(50),
    treatment_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_claim_no (claim_no),
    INDEX idx_employee_number (employee_number)
);

-- Medical Claim Register table (processed claims)
CREATE TABLE medical_claim_register (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_no VARCHAR(50) NOT NULL,
    employee_number VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    designation VARCHAR(100),
    organization VARCHAR(100),
    claim_type VARCHAR(50),
    date_of_application DATE,
    start_date_of_expense DATE,
    end_date_of_expense DATE,
    claim_amount DECIMAL(10,2),
    sanctioned_amount DECIMAL(10,2),
    disallowed_amount DECIMAL(10,2),
    status VARCHAR(50),
    patient_name VARCHAR(100),
    relation VARCHAR(50),
    treatment_type VARCHAR(100),
    claim_received_date DATE,
    disease_name VARCHAR(200),
    dr_name VARCHAR(100),
    hospital_stay ENUM('yes', 'no') DEFAULT 'no',
    ph_status ENUM('SDN', 'DN', 'CR', 'CO', 'DR', 'In Payment', 'Paid', 'Return') DEFAULT 'SDN',
    paid_salary_month VARCHAR(20),
    dr_voucher_no VARCHAR(50),
    dr_amount DECIMAL(10,2),
    processed_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (processed_by) REFERENCES users(id),
    INDEX idx_claim_no (claim_no),
    INDEX idx_ph_status (ph_status)
);

-- Consultation Register table
CREATE TABLE consultation_register (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_no VARCHAR(50) NOT NULL,
    employee_number VARCHAR(50),
    name VARCHAR(100),
    consultation_date DATE,
    consultation_amount DECIMAL(10,2),
    reconciliation_date DATE,
    reconciliation_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_claim_no (claim_no)
);

-- Section Amount table
CREATE TABLE section_amount (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_no VARCHAR(50) NOT NULL,
    employee_number VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    claim_amount DECIMAL(10,2),
    sanctioned_amount DECIMAL(10,2),
    disallowed_amount DECIMAL(10,2),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_claim_no (claim_no)
);

-- Dr Forwarding table
CREATE TABLE dr_forwarding (
    id INT AUTO_INCREMENT PRIMARY KEY,
    forwarding_date DATE NOT NULL,
    forwarded_by INT NOT NULL,
    claim_ids JSON,
    total_bills INT,
    total_amount DECIMAL(10,2),
    status ENUM('pending', 'submitted', 'received') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (forwarded_by) REFERENCES users(id)
);

-- Dr Payment Voucher table
CREATE TABLE dr_payment_voucher (
    id INT AUTO_INCREMENT PRIMARY KEY,
    voucher_no VARCHAR(50) UNIQUE NOT NULL,
    voucher_date DATE NOT NULL,
    doctor_id INT,
    total_bills INT,
    hospital_stay_bills INT,
    consultation_bills INT,
    total_amount DECIMAL(10,2),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES doctors_register(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (employee_no, password, name, role) VALUES 
('ADMIN001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'Admin');

-- Insert default roles
INSERT INTO roles (role_name, permissions) VALUES 
('Admin', '{"dashboard": true, "dr_list": true, "claim_master": true, "entry_form": true, "medical_claim_register": true, "consultation_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true, "user_management": true}'),
('SDN', '{"dashboard": true, "entry_form": true, "medical_claim_register": true}'),
('DN', '{"dashboard": true, "entry_form": true, "medical_claim_register": true, "dr_forwarding": true, "section_amount": true}'),
('CR', '{"dashboard": true, "entry_form": true, "medical_claim_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true}'),
('CO', '{"dashboard": true, "dr_list": true, "claim_master": true, "entry_form": true, "medical_claim_register": true, "consultation_register": true, "dr_forwarding": true, "dr_payment_voucher": true, "section_amount": true, "reports": true}');

-- Insert sample doctors
INSERT INTO doctors_register (name, degree, mobile_no, email, address) VALUES 
('Dr. Rajesh Kumar', 'MBBS, MD', '**********', '<EMAIL>', '123 Medical Street, City'),
('Dr. Priya Sharma', 'MBBS, MS', '**********', '<EMAIL>', '456 Health Avenue, City'),
('Dr. Amit Patel', 'MBBS, DM', '**********', '<EMAIL>', '789 Care Road, City');
